import { Builder } from 'builder-pattern';

import { ICampaign, newDbId } from '@malou-io/package-models';
import { CampaignType, ContactMode, PlatformKey } from '@malou-io/package-utils';

// -----------------------------------------------------
type CampaignPayload = ICampaign;

const _buildCampaign = (campaign: CampaignPayload) => Builder<CampaignPayload>(campaign);

export const getDefaultCampaign = () =>
    _buildCampaign({
        _id: newDbId(),
        restaurantId: newDbId(),
        startDate: new Date(),
        endDate: new Date(),
        name: 'My Campaign',
        contactMode: ContactMode.EMAIL,
        platformKey: PlatformKey.GMB,
        type: CampaignType.REVIEW_BOOSTER,
        privateReviewRatings: [1, 2, 3],
        minDaysFromLastContactedAt: '10',
        createdAt: new Date(),
        updatedAt: new Date(),
        audience: {
            sources: [],
            minDaysFromLastContactedAt: 0,
        },
        content: {
            _id: newDbId().toString(),
            from: {
                name: 'Malou Admin',
                email: '<EMAIL>',
            },
            object: 'Mail object',
            messageHTML: 'Hello',
        },
        contactInteractions: [],
    });
