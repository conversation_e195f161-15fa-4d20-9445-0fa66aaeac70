import fs from 'fs';
import { difference, isNil, omit } from 'lodash';
import { DateTime } from 'luxon';
import { MongoBulkWriteError } from 'mongodb';
import { autoInjectable } from 'tsyringe';

import { UpdateClientBodyDto } from '@malou-io/package-dto';
import { DbId, IClient, ID, ReadPreferenceMode, toDbId, toDbIds } from '@malou-io/package-models';
import { ClientFileError, ClientSource, ContactMode, DateStringFormat, FileType, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import ClientsMapper from ':modules/clients/clients.mapper';
import ClientsRepository from ':modules/clients/clients.repository';
import { CsvParser, XlsxParser } from ':modules/clients/clients.sheet-parser';

interface PlatformRequirements {
    key: string;
    headers: {
        fr: string[];
    };
    required: {
        fr: string[];
    };
}

@autoInjectable()
export default class ClientsUseCases {
    constructor(private _clientsRepository: ClientsRepository) {}

    async getClientsForCampaignRange(
        restaurantId: ID,
        startDate: Date,
        endDate: Date,
        minDaysFromLastContactedAt: number,
        sources: string[],
        contactMode: ContactMode,
        contactModeField: string
    ) {
        return this._clientsRepository.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                [contactModeField]: { $exists: true, $ne: null },
                lastVisitedAt: { $gte: startDate, $lte: endDate },
                source: { $in: sources },
                accepts: contactMode,
                ...(+minDaysFromLastContactedAt
                    ? {
                          $or: [
                              {
                                  lastContactedAt: { $exists: false },
                              },
                              {
                                  lastContactedAt: { $lte: DateTime.local().minus({ days: +minDaysFromLastContactedAt }).toJSDate() },
                              },
                          ],
                      }
                    : {}),
            },
            options: {
                lean: true,
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
            },
        });
    }

    async duplicateClient(data): Promise<IClient> {
        const newClient = {
            restaurantId: data.restaurantId,
            civility: data.civility,
            firstName: data.firstName,
            lastName: data.lastName,
            address: data.address,
            phone: data.phone,
            email: data.email,
            language: data.language,
            source: data.source,
            birthday: data.birthday,
            lastVisitedAt: data.lastVisitedAt,
            lastContactedAt: data.lastContactedAt,
            contactCount: data.contactCount,
            visitCount: data.visitCount,
            accepts: data.accepts,
            reviews: data.reviews,
            duplicatedFromRestaurantId: data.fromRestId,
        };

        return this._clientsRepository.create({ data: newClient });
    }

    _getFileExtension = (file: Express.Multer.File): string => {
        const extension = file.originalname.split('.').pop();
        if (extension === FileType.CSV || extension === FileType.XLS || extension === FileType.XLSX) {
            return extension;
        }
        throw new MalouError(MalouErrorCode.CLIENT_CANNOT_PARSE_FILE, {
            message: 'Cannot parse file',
            metadata: { extension },
        });
    };

    _getLang = (sheetHeaders: string[], platformRequirements: PlatformRequirements) => {
        let maxDiff = 1000;
        let bestLangFound = 'fr';
        for (const lang in platformRequirements.required) {
            if (platformRequirements.required?.[lang]) {
                const headersFound = difference(platformRequirements.required?.[lang], sheetHeaders).length;
                if (headersFound < maxDiff) {
                    maxDiff = headersFound;
                    bestLangFound = lang;
                }
                if (headersFound === 0) {
                    return lang;
                }
            }
        }
        throw new MalouError(MalouErrorCode.CLIENT_LANGUAGE_NOT_FOUND__REQUIRED_HEADERS, {
            message: 'Language not found',
            metadata: { lang: bestLangFound, requiredHeaders: platformRequirements?.required?.[bestLangFound] },
        });
    };

    _getMappedData = ({ rawSheetData, source, restaurantId, platformMapper }) => {
        const dateFormat = this._getDateFormatFromSheet(rawSheetData, source);
        return rawSheetData.map((client) => ({
            restaurantId,
            source,
            ...platformMapper.mapToMalouClient(client, dateFormat),
        }));
    };

    _getDateFormatFromSheet = (rawSheetData: Record<string, string | Date>[], source: ClientSource): DateStringFormat => {
        const MINIMAL_STATISTICALLY_FINE_ROW_NUMBER = 150;
        switch (source) {
            case ClientSource.MALOU:
                if (rawSheetData?.length < MINIMAL_STATISTICALLY_FINE_ROW_NUMBER) {
                    return DateStringFormat.FR;
                }
                for (const client of rawSheetData) {
                    const lastVisitedColumn = client['Date de la dernière réservation ou du click and collect'];
                    if (!lastVisitedColumn || lastVisitedColumn instanceof Date) {
                        continue;
                    }
                    const match = lastVisitedColumn.slice(0, 2)?.match(/\d{2}/)?.[0];
                    const firstTwoDigits = match ? parseInt(match, 10) : null;
                    if (firstTwoDigits && firstTwoDigits > 12) {
                        return DateStringFormat.FR;
                    }
                }
                return DateStringFormat.EN;
            default:
                return DateStringFormat.FR;
        }
    };

    /**
     * return string value from js object
     * @param {*} obj
     */
    _stringify = (obj) => JSON.stringify(obj);

    /**
     * check if file is valid. if valid file return mapped (to malou) clients, isValid & clients length
     */
    mapFileToMalouClients = async (file: Express.Multer.File, restaurantId: ID, source: string) => {
        try {
            let extension = this._getFileExtension(file);
            if (extension === 'xls') {
                const binary = fs.readFileSync(file.path, 'binary');
                const headers = CsvParser._getHeadersArray({ binary });
                if (headers?.sheetHeaders?.length) {
                    extension = 'csv'; // use csv parser if we can
                } else {
                    extension = 'xlsx'; // otherwise use xlsx parser
                }
            }
            const binary = fs.readFileSync(file.path);
            const Parser = {
                xlsx: XlsxParser,
                csv: CsvParser,
            }[extension];
            if (!Parser) {
                throw new MalouError(MalouErrorCode.CLIENT_CANNOT_PARSE_FILE, {
                    message: 'Client parser not found for extension',
                    metadata: { extension },
                });
            }
            const { sheetHeaders, ignoreRowNb } = Parser._getHeadersArray({ binary }) as { sheetHeaders: string[]; ignoreRowNb?: number };
            const fileFormats = tempClientsFilesFormat;
            const platformRequirements = fileFormats.find((cf) => cf.key === source);
            if (!platformRequirements) {
                throw new MalouError(MalouErrorCode.CLIENT_CANNOT_PARSE_FILE, {
                    message: 'Cannot find platform requirements',
                    metadata: { source },
                });
            }
            const lang = this._getLang(sheetHeaders, platformRequirements);
            const PlatformMapper = ClientsMapper._getMapper(source);
            const platformMapper = new PlatformMapper({ lang });
            const filterRecordsFct = platformMapper.filterRecords;
            const rawSheetData = Parser._getSheetData({ binary, filterRecordsFct, ignoreRowNb });
            // eslint-disable-next-line no-empty-function
            fs.unlink(file.path, function () {});
            if (!rawSheetData?.length) {
                return {
                    isValid: false,
                    err: ClientFileError.EMPTY,
                };
            }
            let missingHeaders =
                platformRequirements?.headers?.[lang]?.reduce(
                    (acc, next) => (sheetHeaders.find((h) => h.toLowerCase() === next.toLowerCase()) ? acc : [...acc, next]),
                    []
                ) || [];
            const missingMatchedHeaders =
                platformRequirements?.matchedHeaders?.[lang]?.reduce(
                    (acc, next) => (sheetHeaders.find((h) => h.match(next)) ? acc : [...acc, next]),
                    []
                ) || [];
            missingHeaders = missingHeaders.concat(missingMatchedHeaders);
            const missingRequired = platformRequirements?.required?.[lang].reduce(
                (acc, next) => (missingHeaders.find((h) => h.toLowerCase() === next.toLowerCase()) ? [...acc, next] : acc),
                []
            );
            if (missingRequired.length) {
                return {
                    isValid: false,
                    err: ClientFileError.MISSING,
                };
            }
            const mappedData = this._getMappedData({
                rawSheetData,
                source,
                restaurantId,
                platformMapper,
            });
            return {
                isValid: true,
                fileLength: mappedData.length,
                clients: mappedData,
            };
        } catch (error) {
            // eslint-disable-next-line no-empty-function
            fs.unlink(file.path, function () {});
            throw error;
        }
    };

    /**
     * save clients in db
     * @param {Object[]} clients
     */
    // TO DO: better output type
    importClients = async (clients: IClient[], restaurantId: DbId): Promise<any> => {
        const duplicates = await this._clientsRepository.find({
            filter: {
                restaurantId,
                $or: [
                    { email: { $in: clients.map((c) => c.email).filter((email) => !!email) } },
                    { phone: { $in: clients.map((c) => c.phone).filter((phone) => !!phone) } },
                ],
            },
            options: { lean: true },
        });

        let imported: IClient[] = [];
        try {
            imported = await this._clientsRepository.createMany({ data: clients, options: { ordered: false, lean: true } });
        } catch (error: any) {
            if (error.code === 11000) {
                imported = error.insertedDocs?.map((c) => c.toJSON()) ?? [];
            }
        }
        return { imported, duplicates };
    };

    updateDuplicates = async (clients: IClient[]): Promise<any> => {
        const clientsWithAtLeastEmailOrPhone = clients.filter((c) => c.email || c.phone);
        try {
            return await this._clientsRepository.bulkOperations({
                operations: clientsWithAtLeastEmailOrPhone.map((dC) => ({
                    updateOne: {
                        filter: { _id: dC._id },
                        update: dC,
                    },
                })),
                options: { ordered: false },
            });
        } catch (e) {
            if (e instanceof MongoBulkWriteError && e.result) {
                return e.result;
            }
            throw e;
        }
    };

    /**
     * remove duplicates from list of clients
     * @param {Client[]} clients
     * @param {string} field
     */
    clearDuplicates = (clients, field) => {
        const nullFieldRecords: IClient[] = [];
        const fieldRecords: IClient[] = [];

        clients.forEach((client) => {
            if (client[field]) {
                fieldRecords.push(client);
            } else {
                nullFieldRecords.push(client);
            }
        });

        const sortedClients = fieldRecords.sort((a, b) => (this._stringify(a[field]) > this._stringify(b[field]) ? 1 : -1));
        let i = 0;
        let j = 1;
        logger.info('[WHILE_TRACKER] - clearDuplicates started');
        while (j < sortedClients.length) {
            if (JSON.stringify(sortedClients[i][field]) !== JSON.stringify(sortedClients[j][field])) {
                i++;
                sortedClients[i] = sortedClients[j];
                j++;
            } else {
                let k = j;
                while (k < sortedClients.length && this._stringify(sortedClients[k][field]) === this._stringify(sortedClients[i][field])) {
                    if (Object.keys(sortedClients[k]).length > Object.keys(sortedClients[i]).length) {
                        sortedClients[i] = sortedClients[k];
                    }
                    k++;
                }
                j = k;
            }
        }
        logger.info('[WHILE_TRACKER] - clearDuplicates ended');
        return sortedClients.splice(0, i + 1).concat(nullFieldRecords);
    };

    // TODO: remove any @hamza
    createClient(client: any): Promise<IClient> {
        if (client.address && Object.values(client.address).every(isNil)) {
            client = omit(client, 'address');
        }
        return this._clientsRepository.create({
            data: client,
        });
    }

    async getClientsByRestaurantId(restaurantId: ID) {
        return this._clientsRepository.find({ filter: { restaurantId: toDbId(restaurantId) }, options: { lean: true } });
    }

    async getClientByIdOrFail(clientId: ID) {
        return this._clientsRepository.findOneOrFail({ filter: { _id: toDbId(clientId) }, options: { lean: true } });
    }

    async getClientByFilter(filter: Partial<IClient>) {
        return this._clientsRepository.findOne({ filter, options: { lean: true } });
    }

    async upsertClientById(clientId: ID, data: Partial<IClient>) {
        if (data.reviewsLeft?.length) {
            return this._clientsRepository.findOneAndUpdate({
                filter: { _id: toDbId(clientId) },
                update: {
                    $set: omit(data, 'reviewsLeft'),
                    $push: {
                        reviewsLeft: { $each: data.reviewsLeft },
                    },
                },
            });
        }
        return this._clientsRepository.upsert({
            filter: { _id: toDbId(clientId) },
            update: omit(data, 'reviewsLeft'),
            options: { lean: true },
        });
    }

    async updateClientByFilter(filter: Partial<IClient>, data: Partial<IClient>, options = {}) {
        return this._clientsRepository.findOneAndUpdate({
            filter,
            update: {
                $setOnInsert: {
                    source: data.source,
                },
                $set: omit(data, 'source'),
            },
            options,
        });
    }

    async deleteManyClientsByIds(clientsIds: ID[]) {
        return this._clientsRepository.deleteMany({ filter: { _id: { $in: toDbIds(clientsIds) } } });
    }

    async deleteClientById(clientId: ID) {
        return this._clientsRepository.deleteOne({ filter: { _id: toDbId(clientId) } });
    }

    async updateClientHasLeftReview(clientId: ID, platformKey: string) {
        return this._clientsRepository.findOneAndUpdate({
            filter: { _id: toDbId(clientId) },
            update: {
                $addToSet: {
                    reviewsLeft: {
                        platformKey,
                        hasLeftReview: true,
                    },
                },
            },
        });
    }

    async getDuplicateClients(clients: IClient[], restaurantId: ID) {
        return this._clientsRepository.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                $or: [
                    {
                        email: {
                            $in: clients.map((c) => c.email).filter((email) => !!email),
                        },
                    },
                    {
                        phone: {
                            $in: clients.map((c) => c.phone).filter((phone) => !!phone),
                        },
                    },
                ],
            },
        });
    }

    async upsertClientByFilter(filter: UpdateClientBodyDto['filter'], data: UpdateClientBodyDto['data']) {
        return this._clientsRepository.upsertClientByFilter(filter, data);
    }
}

const tempClientsFilesFormat = Object.freeze([
    {
        key: 'zenchef',
        headers: {
            fr: ['Prénom', 'Nom', 'Email', 'Téléphone', 'Pays', 'lang', 'created_at', 'updated_at'],
            en: ['First Name', 'Last Name', 'Phone', 'Email', 'Lang', 'created_at', 'updated_at'],
        },
        required: {
            fr: ['Prénom', 'Nom', 'Téléphone', 'Email'],
            en: ['First Name', 'Last Name', 'Phone', 'Email'],
        },
    },
    {
        key: 'pulp',
        headers: { fr: [''] },
        required: { fr: [''] },
    },
    {
        key: 'lafourchette',
        headers: {
            fr: [
                'Civilité',
                'Prénom',
                'Nom',
                'Pays',
                'Ville',
                'Code Postal',
                'Adresse',
                'Mobile Personnel',
                'Email personnel',
                'Langue',
                "Date d'inscription",
                'Newsletter',
                'Date de naissance',
                'Statut',
                'VIP',
                'Fax Personnel',
                "Complément d'adresse",
                'Prescripteur',
                'Commission négociée',
                "Nombre d'apport d'affaire total",
                "Chiffre d'affaire apporté total",
                'Entreprise',
                'Fonction',
                'Email Pro',
                'Mobile Pro',
                'Téléphone pro',
                'Pays Pro',
                'Adresse Pro',
                "Complément d'adresse Pro",
                'Ville Pro',
                'Allergie',
                'Nom Allergie',
                'Végétarien',
                'Handicap',
                'Préférence Nourriture',
                'Tables préférées',
                'Note',
                'Newsletter',
                'Restaurant de référence',
                'Date de mise à jour',
                "Date d'inscription",
                "Nombre d'annulation",
                'Nombre de no-show',
                'Total dépense',
            ],
            en: [
                'Title',
                'First Name',
                'Last Name',
                'Birthday',
                'Language',
                'VIP',
                'Rank',
                'Email',
                'Phone number',
                'Secondary phone number',
                'Country',
                'Address',
                'Zipcode',
                'City',
                'Promoter',
                'Allergies',
                'Dietary restrictions',
                'Food preferences',
                'Drinks preferences',
                'Seating preferences',
                'Customer Notes',
                'Newsletter',
                'Creation date',
                'Last update date',
                'Origin restaurant',
                'Entreprise',
            ],
        },
        matchedHeaders: { fr: ['Dernière date de visite', 'Nombre de visites'], en: ['Last visited at', 'Visit count'] },
        required: {
            fr: ['Prénom', 'Nom', 'Mobile Personnel', 'Email personnel'],
            en: ['First Name', 'Last Name', 'Phone number', 'Email'],
        },
    },
    {
        key: 'zelty',
        headers: {
            fr: [
                'Nom',
                'Prénom',
                'N° de rue',
                'Ville',
                'Code postal',
                'Nom de rue',
                'N° de rue',
                'Téléphone',
                'Mail',
                "Date d'inscription",
                'Optin Mail',
                'CA actuel',
                'Optin SMS',
                'Statut',
                'Date de naissance',
                'Dernier restaurant',
                'Date de la dernière commande',
                'Nombre de commandes',
                'CA',
                'Points de fidélités',
                'Info interne',
                'Info client',
                "Complément d'adresse",
                'Batiment',
                'Porte',
                'Étage',
                'Digicode',
                'Identifiant externe',
                'Carte fidélité',
                'VIP',
                'Téléphone 2',
                'Entreprise',
                'ID',
            ],
        },
        required: { fr: ['Nom', 'Prénom', 'Téléphone', 'Mail'] },
    },
    {
        key: 'malou',
        headers: {
            fr: [
                'Civilité (H/F/NSP)',
                'Prénom',
                'Nom',
                'Pays',
                'Ville',
                'Code Postal',
                'Numéro de téléphone',
                'Email',
                'Langue parlée',
                'Date de la dernière réservation ou du click and collect',
                'Nombre de visites/réservations',
                'Accepte de recevoir des mails',
                'Accepte de recevoir des sms',
            ],
        },
        required: { fr: ['Nom', 'Prénom', 'Numéro de téléphone', 'Email'] },
    },
]);
