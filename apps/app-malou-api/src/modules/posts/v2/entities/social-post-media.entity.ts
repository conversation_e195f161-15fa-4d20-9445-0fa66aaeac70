import { SocialPostMediaDto } from '@malou-io/package-dto';
import { IMediaDimension, IMediaTransformData } from '@malou-io/package-models';
import { EntityConstructor, MediaType } from '@malou-io/package-utils';

type SocialPostMediaProps = EntityConstructor<SocialPostMedia>;

// todo posts-v2 after v1 is deleted, run a migration to add thumbnail & transformData to all media and then,
//  remove url form this class and set thumbnailUrl & transformData to required
export class SocialPostMedia {
    url: string;
    type: MediaType;

    /**
     * This URL can target an object in the Malou’s S3 bucket (if the post has been edited on the
     * Malou app) or a picture on an external CDN (if the post has been fetched from Facebook or
     * or Instagram).
     */
    thumbnailUrl?: string;

    thumbnailDimensions?: IMediaDimension;
    transformData?: IMediaTransformData;

    duration?: number;

    constructor(data: SocialPostMediaProps) {
        this.url = data.url;
        this.type = data.type;
        this.thumbnailUrl = data.thumbnailUrl;
        this.thumbnailDimensions = data.thumbnailDimensions;
        this.transformData = data.transformData;
        this.duration = data.duration;
    }

    toDto(): SocialPostMediaDto {
        return {
            url: this.url,
            type: this.type,
            thumbnailUrl: this.thumbnailUrl,
            thumbnailDimensions: this.thumbnailDimensions,
            transformData: this.transformData,
            duration: this.duration,
        };
    }
}
