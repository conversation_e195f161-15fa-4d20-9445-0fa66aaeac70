import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { EntityRepository, INfcWithRestaurant, ISticker, StickerModel, toDbId } from '@malou-io/package-models';
import { MalouErrorCode, NfcType } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Sticker, StickerProps } from ':modules/nfc/stickers/entities/sticker.entity';

import { NfcWithRestaurant } from '../entities/nfc-with-restaurant.entity';
import NfcsRepository from '../nfcs.repository';

@singleton()
export class StickersRepository extends EntityRepository<ISticker> {
    constructor(private readonly _nfcsRepository: NfcsRepository) {
        super(StickerModel);
    }

    async upsertSticker(data: Omit<StickerProps, 'id' | 'createdAt' | 'updatedAt'>): Promise<Sticker> {
        const stickerDocument = await this.upsert({
            filter: { restaurantId: toDbId(data.restaurantId) },
            update: this.toDocumentWithoutId(data),
            options: { lean: true },
        });
        return this.toEntity(stickerDocument);
    }

    async updateSticker(id: string, data: Partial<StickerProps>): Promise<Sticker> {
        const stickerDocument = await this.findOneAndUpdate({
            filter: { _id: toDbId(id) },
            update: this.toPartialDocument(data),
            options: { lean: true },
        });
        assert(stickerDocument);
        return this.toEntity(stickerDocument);
    }

    async findStickerByRestaurantId(restaurantId: string): Promise<NfcWithRestaurant> {
        const stickerDocument = await this.findOne({
            filter: { restaurantId: toDbId(restaurantId) },
            options: {
                lean: true,
                populate: [
                    {
                        path: 'restaurant',
                        populate: [
                            {
                                path: 'logoPopulated',
                            },
                            {
                                path: 'coverPopulated',
                            },
                        ] as any,
                    },
                ],
            },
        });

        if (!stickerDocument) {
            throw new MalouError(MalouErrorCode.STICKER_NOT_FOUND, {
                message: `Sticker not found for restaurant`,
                metadata: { restaurantId },
            });
        }
        return this._nfcsRepository.toNfcWithRestaurantEntity(stickerDocument as INfcWithRestaurant);
    }

    toEntity(document: ISticker): Sticker {
        return new Sticker({
            id: document._id.toString(),
            restaurantId: document.restaurantId.toString(),
            chipName: document.chipName ?? undefined,
            notes: document.notes ?? undefined,
            platformKey: document.platformKey,
            redirectionLink: document.redirectionLink,
            active: document.active,
            starsRedirected: document.starsRedirected,
            createdAt: document.createdAt,
            updatedAt: document.updatedAt,
        });
    }

    toDocument(entity: StickerProps): Omit<ISticker, 'createdAt' | 'updatedAt'> {
        return {
            ...this.toDocumentWithoutId(entity),
            _id: toDbId(entity.id),
        };
    }

    toPartialDocument(entity: Partial<StickerProps>): Partial<ISticker> {
        return {
            ...this.toPartialDocumentWithoutId(entity),
            _id: entity.id ? toDbId(entity.id) : undefined,
        };
    }

    toDocumentWithoutId(entity: Omit<StickerProps, 'id' | 'createdAt' | 'updatedAt'>): Omit<ISticker, '_id' | 'createdAt' | 'updatedAt'> {
        return {
            restaurantId: toDbId(entity.restaurantId),
            chipName: entity.chipName ?? null,
            notes: entity.notes,
            platformKey: entity.platformKey,
            redirectionLink: entity.redirectionLink,
            active: entity.active,
            starsRedirected: entity.starsRedirected,
            type: NfcType.STICKER,
        };
    }

    toPartialDocumentWithoutId(entity: Partial<Omit<StickerProps, 'id'>>): Partial<Omit<ISticker, '_id'>> {
        return {
            restaurantId: entity.restaurantId ? toDbId(entity.restaurantId) : undefined,
            chipName: entity.chipName ?? null,
            notes: entity.notes,
            platformKey: entity.platformKey,
            redirectionLink: entity.redirectionLink,
            active: entity.active,
            starsRedirected: entity.starsRedirected,
            type: NfcType.STICKER,
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
        };
    }
}
