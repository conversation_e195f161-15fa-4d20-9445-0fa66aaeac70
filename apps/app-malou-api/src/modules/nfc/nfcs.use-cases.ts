import { autoInjectable } from 'tsyringe';

import { INfcWithRestaurant } from '@malou-io/package-models';

import NfcsRepository from './nfcs.repository';

@autoInjectable()
export default class NfcsUseCases {
    constructor(private _nfcsRepository: NfcsRepository) {}

    async deleteNfc(nfcId: string): Promise<void> {
        await this._nfcsRepository.deleteOne({ filter: { _id: nfcId } });
    }

    async getNfcByChipName(chipName: string): Promise<INfcWithRestaurant> {
        const nfc = await this._nfcsRepository.findOneOrFail({
            filter: { chipName },
            options: {
                populate: [
                    {
                        path: 'restaurant',
                        populate: [{ path: 'logoPopulated' }, { path: 'coverPopulated' }] as any,
                    },
                ],
                lean: true,
            },
        });
        return nfc as INfcWithRestaurant;
    }
}
