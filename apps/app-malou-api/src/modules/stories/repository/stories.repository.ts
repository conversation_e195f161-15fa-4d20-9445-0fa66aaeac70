import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { EntityRepository, IMedia, IPost, PostModel, ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import {
    MediaType,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    SocialAttachmentsMediaTypes,
    StoriesListFilter,
} from '@malou-io/package-utils';

import { GetMediaInfoService } from ':modules/media/services/get-media-thumbnail/get-media-thumbnail.service';
import { PostAuthor } from ':modules/posts/v2/entities/author.entity';
import { SocialPostMedia } from ':modules/posts/v2/entities/social-post-media.entity';
import {
    errorStage,
    feedbackMessageCountProjectionStages,
    firstAttachmentPopulationStages,
    limitStage,
    platformsKeysProjectionStage,
    sortStage,
} from ':modules/posts/v2/repository/posts.pipeline';
import { StoryItem } from ':modules/stories/entities/story-item';
import { countStoriesStages, projectStoryItemStage } from ':modules/stories/repository/stories.pipeline';
import { IStoryItem } from ':modules/stories/repository/story-repository.types';

/**
 * !!! README !!!
 * This repository is linked to the POSTS collection in MongoDB
 * You should always filter the stories with the `isStory: true` condition
 * in order not to impact the other types of posts.
 */
@singleton()
export class StoriesRepository extends EntityRepository<IPost> {
    constructor(private readonly _getMediaInfoService: GetMediaInfoService) {
        super(PostModel);
    }

    async getStories(restaurantId: string, cursor: null | Date, limit: number, filter: StoriesListFilter | null): Promise<StoryItem[]> {
        if (filter === StoriesListFilter.FEEDBACK) {
            return this._getStoriesWithFeedback(restaurantId, cursor, limit);
        }
        const dbFilter = this._getDbFilterFromStoriesListFilter(filter);
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: true,
                ...(dbFilter && { ...dbFilter }),
                ...(cursor && { sortDate: { $lt: cursor } }),
            },
        };

        const pipeline = [
            matchStage,
            sortStage,
            limitStage(limit),
            ...feedbackMessageCountProjectionStages,
            platformsKeysProjectionStage,
            ...firstAttachmentPopulationStages,
            errorStage,
            projectStoryItemStage,
        ];

        const posts = (await this.aggregate(pipeline)) as IStoryItem[];

        const results: StoryItem[] = [];
        for (const story of posts) {
            results.push(await this._toStoryItemEntity(story));
        }
        return results;
    }

    async getStoriesCounts(restaurantId: string): Promise<{ total?: number; draft?: number; error?: number; feedbacks?: number }> {
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: true,
            },
        };

        const pipeline = [matchStage, ...feedbackMessageCountProjectionStages, ...countStoriesStages()];

        const result = (await this.aggregate(pipeline, { readPreference: ReadPreferenceMode.SECONDARY_PREFERRED }))[0] as {
            total?: number;
            draft?: number;
            error?: number;
            feedbacks?: number;
        };
        return result;
    }

    async getPublishedStoriesCount(restaurantId: string, startDate: Date, endDate: Date): Promise<number> {
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: true,
                published: PostPublicationStatus.PUBLISHED,
                socialCreatedAt: {
                    $gte: startDate,
                    $lte: endDate,
                },
            },
        };

        const pipeline = [
            matchStage,
            {
                $count: 'count',
            },
        ];

        const result = (await this.aggregate(pipeline, { readPreference: ReadPreferenceMode.SECONDARY_PREFERRED }))[0] as {
            count?: number;
        };
        return result?.count ?? 0;
    }

    private _getDbFilterFromStoriesListFilter(
        filter: StoriesListFilter | null
    ): { published: PostPublicationStatus; createdAt?: { $gt: Date } } | null {
        switch (filter) {
            case StoriesListFilter.DRAFT:
                return { published: PostPublicationStatus.DRAFT };
            case StoriesListFilter.ERROR:
                return { published: PostPublicationStatus.ERROR, createdAt: { $gt: DateTime.now().minus({ months: 6 }).toJSDate() } };
            default:
                return null;
        }
    }

    private async _getStoriesWithFeedback(restaurantId: string, cursor: null | Date, limit: number): Promise<StoryItem[]> {
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: true,
                feedbackId: { $ne: null },
                ...(cursor && { sortDate: { $lt: cursor } }),
            },
        };
        const secondMatchStage = {
            $match: {
                feedbackMessageCount: { $gt: 0 },
            },
        };

        const pipeline = [
            matchStage,
            ...feedbackMessageCountProjectionStages,
            secondMatchStage,
            sortStage,
            limitStage(limit),
            platformsKeysProjectionStage,
            ...firstAttachmentPopulationStages,
            errorStage,
            projectStoryItemStage,
        ];
        const posts = (await this.aggregate(pipeline)) as IStoryItem[];

        const results: StoryItem[] = [];
        for (const story of posts) {
            results.push(await this._toStoryItemEntity(story));
        }
        return results;
    }

    private async _toStoryItemEntity(story: IStoryItem): Promise<StoryItem> {
        return new StoryItem({
            id: story._id.toString(),
            platformKeys: story.platformKeys,
            published: story.published,
            isPublishing: story.isPublishing ?? false,
            feedbackMessageCount: story.feedbackMessageCount,
            plannedPublicationDate: story.plannedPublicationDate ?? undefined,
            media: await this._getMediaFromIStoryItem(story),
            socialLink: story.socialLink,
            socialCreatedAt: story.socialCreatedAt,
            sortDate: story.sortDate ?? story.socialCreatedAt ?? story.plannedPublicationDate ?? new Date(),
            author: story.author
                ? new PostAuthor({
                      id: story.author._id.toString(),
                      name: story.author.name,
                      lastname: story.author.lastname,
                      picture: story.author.picture ?? undefined,
                  })
                : undefined,
            mostRecentPublicationErrorCode: story.mostRecentPublicationErrorCode,
            bindingId: story.bindingId,
            createdFromDeviceType: story.createdFromDeviceType,
        });
    }

    /** Returns the thumbnail to display in the post list or in the feed */
    private async _getMediaFromIStoryItem(item: {
        firstAttachment?: IMedia;
        socialAttachments?: IPost['socialAttachments'];
        platformKeys?: PlatformKey[];
    }): Promise<SocialPostMedia | null> {
        if (item.socialAttachments?.[0]) {
            return new SocialPostMedia({
                url: item.socialAttachments[0].urls.original,
                type: this._socialAttachmentsMediaTypesToMediaType(item.socialAttachments[0].type),
                thumbnailUrl: item.socialAttachments[0].thumbnailUrl ?? undefined,
                thumbnailDimensions: undefined,
                transformData: undefined,
                duration: undefined,
            });
        }
        if (item.firstAttachment) {
            return await this._IMediaToSocialPostMedia(item.firstAttachment);
        }
        return null;
    }

    private async _IMediaToSocialPostMedia(media: IMedia): Promise<SocialPostMedia | null> {
        const urlAndDimensions = this._getMediaInfoService.getUrlAndDimensions(media);
        const thumbnail256OutsideUrlAndDimensions = await this._getMediaInfoService.getThumbnail256OutsideUrlAndDimensions(media);

        if (!urlAndDimensions || !thumbnail256OutsideUrlAndDimensions) {
            return null;
        }

        return new SocialPostMedia({
            url: urlAndDimensions.url,
            type: media.type,
            thumbnailUrl: thumbnail256OutsideUrlAndDimensions.url,
            thumbnailDimensions: thumbnail256OutsideUrlAndDimensions.dimensions,
            transformData: media.transformData,
            duration: media.duration ?? undefined,
        });
    }

    private _socialAttachmentsMediaTypesToMediaType(socialAttachmentsMediaType: SocialAttachmentsMediaTypes): MediaType {
        switch (socialAttachmentsMediaType) {
            case SocialAttachmentsMediaTypes.IMAGE:
                return MediaType.PHOTO;
            case SocialAttachmentsMediaTypes.VIDEO:
                return MediaType.VIDEO;
        }
    }
}
