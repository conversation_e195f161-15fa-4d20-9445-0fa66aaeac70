import { ForbiddenError, subject } from '@casl/ability';
import type { NextFunction, Request, Response } from 'express';
import { omit } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    ActivateYextParamsDto,
    AddRestaurantForUserWithEmailBodyDto,
    addRestaurantForUserWithEmailBodyValidator,
    AddRestaurantForUserWithEmailParamsDto,
    addRestaurantForUserWithEmailParamsValidator,
    AddRestaurantsForUserWithEmailBodyDto,
    addRestaurantsForUserWithEmailBodyValidator,
    AddRestaurantsForUserWithEmailParamsDto,
    addRestaurantsForUserWithEmailParamsValidator,
    AddUserToRestaurantWithEmailResponseDto,
    AdminUpdateRestaurantBodyDto,
    adminUpdateRestaurantBodyValidator,
    AdminUpdateRestaurantParamsDto,
    adminUpdateRestaurantParamsValidator,
    CheckRestaurantIdInParamsDto,
    checkRestaurantIdInParamsValidator,
    CreatePlatformAccessBodyDto,
    createPlatformAccessBodyValidator,
    CreatePlatformAccessParamsDto,
    createPlatformAccessParamsValidator,
    CreateRestaurantBodyDto,
    createRestaurantBodyValidator,
    CreateRestaurantResponseDto,
    DeactivateYextParamsDto,
    DuplicateSpecialHoursBodyDto,
    duplicateSpecialHoursBodyValidator,
    GetAllRestaurantsQueryDto,
    getAllRestaurantsQueryValidator,
    GetManagersResponseDto,
    GetRestaurantCurrentStateResponseDto,
    GetRestaurantsByIdsQueryDto,
    getRestaurantsByIdsQueryValidator,
    GetRestaurantsForUserQueryDto,
    getRestaurantsForUserQueryValidator,
    GetRestaurantsForUserWithEmailParamsDto,
    getRestaurantsForUserWithEmailParamsValidator,
    GetRestaurantsFromProviderQueryDto,
    getRestaurantsFromProviderQueryValidator,
    GetRestaurantsFromProviderResponseDto,
    GetStoreLocatorOrganizationRestaurantsParamsDto,
    getStoreLocatorOrganizationRestaurantsParamsValidator,
    GetUserWithEmailRestaurantsResponseDto,
    HandleGetOrganizationRestaurantsParamsDto,
    handleGetOrganizationRestaurantsParamsValidator,
    HandleShowPlatformAccessParamsDto,
    handleShowPlatformAccessParamsValidator,
    HandleValidatePlatformAccessBodyDto,
    handleValidatePlatformAccessBodyValidator,
    HandleValidatePlatformAccessParamsDto,
    handleValidatePlatformAccessParamsValidator,
    RemoveUserRestaurantsBodyDto,
    removeUserRestaurantsBodyValidator,
    RestaurantWithoutStickerDto,
    SearchRestaurantsByTextResponseDto,
    SearchRestaurantsQueryDto,
    searchRestaurantsQueryValidator,
    SearchRestaurantsV2QueryDto,
    searchRestaurantsV2QueryValidator,
    StoreLocatorOrganizationRestaurantDto,
    UpdatePlatformAccessStatusBodyDto,
    updatePlatformAccessStatusBodyValidator,
    UpdatePlatformAccessStatusParamsDto,
    updatePlatformAccessStatusParamsValidator,
    UpdateRestaurantActiveBodyDto,
    updateRestaurantActiveBodyValidator,
    UpdateRestaurantBodyDto,
    updateRestaurantBodyValidator,
    UpdateRestaurantCalendarEventsCountryBodyDto,
    updateRestaurantCalendarEventsCountryBodyValidator,
    UpdateRestaurantCalendarEventsCountryParamsDto,
    updateRestaurantCalendarEventsCountryParamsValidator,
    UpdateRestaurantOrganizationParamsDto,
    updateRestaurantOrganizationParamsValidator,
    UpdateRestaurantOrganizationRequestBodyDto,
    updateRestaurantOrganizationRequestBodyValidator,
    UpdateRestaurantParamsDto,
    updateRestaurantParamsValidator,
    UpdateRestaurantsForUserDto,
    updateRestaurantsForUserWithEmailBodyDto,
    updateRestaurantsForUserWithEmailBodyValidator,
    updateRestaurantsForUserWithEmailParamsDto,
    updateRestaurantsForUserWithEmailParamsValidator,
    UpdateSubscriptionsProviderLocationBodyDto,
    updateSubscriptionsProviderLocationBodyValidator,
    UpdateSubscriptionsProviderLocationParamsDto,
    updateSubscriptionsProviderLocationParamsValidator,
    UpdateSubscriptionsProviderLocationResponseDto,
    yextToggleParamsValidator,
} from '@malou-io/package-dto';
import { IRestaurant, toDbId, toDbIds } from '@malou-io/package-models';
import { ApiResult, ApiResultV2, CaslAction, CaslSubject, MalouErrorCode, projectionToArray } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { Body, Params, Query } from ':helpers/decorators/validators';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { RequestWithPermissions } from ':helpers/utils.types';
import { UpdateRestaurantAttributesUseCase } from ':modules/attributes/use-cases/update-restaurant-attributes/update-restaurant-attributes.use-case';
import { PlatformsUseCases } from ':modules/platforms/platforms.use-cases';
import DeleteLocationUseCase from ':modules/publishers/yext/use-cases/delete-location/delete-location.use-case';
import { AddRestaurantToUserConfigurationUseCase } from ':modules/reports/use-cases/add-restaurant-to-user-configuration/add-restaurant-to-user-configuration.use-case';
import { UpdateRestaurantNameService } from ':modules/restaurant-ai-settings/services/update-restaurant-name/update-restaurant-name.service';
import { Access, IRestaurantAccess } from ':modules/restaurants/entities/access.entity';
import { RestaurantsDtoMapper } from ':modules/restaurants/mappers/restaurants.dto-mapper';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import RestaurantsUseCases from ':modules/restaurants/restaurants.use-cases';
import { ComputeRestaurantCompletionScoreService } from ':modules/restaurants/services/compute-completion-score.service';
import YextActivateLocationUseCase from ':modules/restaurants/use-cases/activate-yext-location.use-case';
import { AddRestaurantsForUserUseCase } from ':modules/restaurants/use-cases/add-restaurants-for-user/add-restaurants-for-user.use-case';
import { CreatePlatformAccessUseCase } from ':modules/restaurants/use-cases/create-platform-access.use-case';
import { CreateRestaurantUseCase } from ':modules/restaurants/use-cases/create-restaurant.use-case';
import { DuplicateSpecialHoursUseCase } from ':modules/restaurants/use-cases/duplicate-special-hours.use-case';
import { GetRestaurantsFromProviderUseCase } from ':modules/restaurants/use-cases/get-restaurants-from-provider.use-case';
import { GetRestaurantsForUserUseCase } from ':modules/restaurants/use-cases/get-restaurants-of-user/get-restaurants-of-user.use-case';
import { GetRestaurantsWithoutStickerUseCase } from ':modules/restaurants/use-cases/get-restaurants-without-sticker.use-case';
import { GetStoreLocatorOrganizationRestaurantsUseCase } from ':modules/restaurants/use-cases/get-store-locator-organization-restaurants/get-store-locator-organization-restaurants.use-case';
import { SearchRestaurantsV2UseCase } from ':modules/restaurants/use-cases/search-restaurants-v2/search-restaurants-v2.use-case';
import { UpdatePlatformAccessStatusUseCase } from ':modules/restaurants/use-cases/update-platform-access-status.use-case';
import { UpdateRestaurantActiveUseCase } from ':modules/restaurants/use-cases/update-restaurant-active/update-restaurant-active.use-case';
import { UpdateRestaurantCalendarEventsCountryUseCase } from ':modules/restaurants/use-cases/update-restaurant-calendar-events-country/update-restaurant-calendar-events-country.use-case';
import { UpdateRestaurantOrganizationUseCase } from ':modules/restaurants/use-cases/update-restaurant-organization.use-case';
import { UpdateRestaurantsForUserUseCase } from ':modules/restaurants/use-cases/update-restaurants-for-user/update-restaurants-for-user.use-case';
import { UpdateSubscriptionsProviderLocationUseCase } from ':modules/restaurants/use-cases/update-subscriptions-provider-location.use-case';
import { ValidatePlatformAccessUseCase } from ':modules/restaurants/use-cases/validate-platform-access/validate-platform-access.use-case';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';
import { UserRestaurantsUseCases } from ':modules/user-restaurants/user-restaurants.use-cases';
import { UsersRepository } from ':modules/users/users.repository';

@singleton()
export default class RestaurantsController {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _userRestaurantsRepository: UserRestaurantsRepository,
        private readonly _userRestaurantsUseCases: UserRestaurantsUseCases,
        private readonly _updateRestaurantAttributesUseCase: UpdateRestaurantAttributesUseCase,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _restaurantsMapper: RestaurantsDtoMapper,
        private readonly _restaurantsUseCases: RestaurantsUseCases,
        private readonly _platformsUseCases: PlatformsUseCases,
        private readonly _updateRestaurantOrganizationUseCase: UpdateRestaurantOrganizationUseCase,
        private readonly _yextActivateLocationUseCase: YextActivateLocationUseCase,
        private readonly _createPlatformAccessUseCase: CreatePlatformAccessUseCase,
        private readonly _updatePlatformAccessStatusUseCase: UpdatePlatformAccessStatusUseCase,
        private readonly _validatePlatformAccessUseCase: ValidatePlatformAccessUseCase,
        private readonly _getRestaurantsWithoutStickerUseCase: GetRestaurantsWithoutStickerUseCase,
        private readonly _yextDeleteLocationUseCase: DeleteLocationUseCase,
        private readonly _duplicateSpecialHoursUseCase: DuplicateSpecialHoursUseCase,
        private readonly _addRestaurantToUserConfigurationUseCase: AddRestaurantToUserConfigurationUseCase,
        private readonly _createRestaurantUseCase: CreateRestaurantUseCase,
        private readonly _updateRestaurantActiveUseCase: UpdateRestaurantActiveUseCase,
        private readonly _getRestaurantsForUserUseCase: GetRestaurantsForUserUseCase,
        private readonly _getRestaurantsFromProviderUseCase: GetRestaurantsFromProviderUseCase,
        private readonly _addRestaurantsForUserUseCase: AddRestaurantsForUserUseCase,
        private readonly _updateRestaurantsForUserUseCase: UpdateRestaurantsForUserUseCase,
        private readonly _searchRestaurantsV2UseCase: SearchRestaurantsV2UseCase,
        private readonly _updateRestaurantNameService: UpdateRestaurantNameService,
        private readonly _updateRestaurantCalendarEventsCountryUseCase: UpdateRestaurantCalendarEventsCountryUseCase,
        private readonly _computeRestaurantCompletionScoreService: ComputeRestaurantCompletionScoreService,
        private readonly _getStoreLocatorOrganizationRestaurantsUseCase: GetStoreLocatorOrganizationRestaurantsUseCase,
        private readonly _updateSubscriptionsProviderLocationUseCase: UpdateSubscriptionsProviderLocationUseCase
    ) {}

    @Query(getRestaurantsForUserQueryValidator)
    async handleGetRestaurantsForUser(
        req: Request<any, any, any, GetRestaurantsForUserQueryDto>,
        res: Response<ApiResult<any[]>>,
        next: NextFunction
    ) {
        try {
            let { fields } = req.query;

            const populate: any[] = [];

            if (!fields || fields?.match(/categoryList/)) {
                populate.push({ path: 'categoryList', populate: [{ path: 'platformCategories' }] });
                fields = fields?.replace('categoryList', '');
            }
            if (!fields || fields.match(/category/)) {
                populate.push({ path: 'category', populate: [{ path: 'platformCategories' }] });
            }
            if (!fields || fields.match(/attributeList/)) {
                populate.push({ path: 'attributeList', populate: [{ path: 'attribute' }] });
            }
            if (!fields || fields.match(/informationUpdate/)) {
                populate.push({
                    path: 'informationUpdate',
                    select: { _id: 1, createdAt: 1 },
                    options: { sort: { createdAt: -1 }, limit: 1 },
                });
            }
            if (!fields || fields.match(/logo/)) {
                populate.push({
                    path: 'logo',
                    select: { urls: 1 },
                });
            }
            if (!fields || fields.match(/cover/)) {
                populate.push({
                    path: 'cover',
                    select: { urls: 1 },
                });
            }
            if (!fields || fields.match(/availableHoursTypes/)) {
                populate.push({
                    path: 'availableHoursTypes',
                });
            }
            if (fields.match(/organization/)) {
                populate.push({
                    path: 'organization',
                });
            }
            if (fields.match(/bricks/)) {
                populate.push({
                    path: 'bricks',
                });
            }

            const userWithRestaurants = await this._usersRepository.getUserWithActiveRestaurants(req.user._id);
            const userRestaurants = userWithRestaurants?.restaurants;

            const restaurants = await this._restaurantsRepository.find({
                filter: {
                    _id: toDbIds(userRestaurants?.map((r) => r.restaurantId) ?? []),
                    active: true,
                },
                projection: projectionToArray(fields).reduce((acc, field) => ({ ...acc, [field]: 1 }), {}),
                options: {
                    populate,
                    lean: true,
                },
            });

            if (fields.match(/platformKeys/)) {
                const platformResults = await Promise.all(
                    restaurants.map((restaurant) => this._platformsUseCases.getPlatformsForRestaurantId(restaurant._id.toString()))
                );

                for (let i = 0; i < restaurants.length; i++) {
                    const platforms = platformResults[i];
                    Object.assign(restaurants[i], { platformKeys: platforms.map((platform) => platform.key) });
                }
            }

            // ApiResult cannot be typed right now because we use params fields to get only the fields we need
            return res.json({
                msg: 'Restaurant list',
                data: restaurants,
            });
        } catch (err) {
            return next(err);
        }
    }

    @Query(searchRestaurantsQueryValidator)
    async handleSearchRestaurants(
        req: Request<any, any, any, SearchRestaurantsQueryDto>,
        res: Response<ApiResult<SearchRestaurantsByTextResponseDto[]>>,
        next: NextFunction
    ) {
        try {
            const { text } = req.query;
            const restaurants = await this._restaurantsRepository.find({
                filter: { name: { $regex: text, $options: 'i' } },
                projection: { name: 1 },
                options: {
                    sort: { createdAt: -1 },
                    lean: true,
                },
            });
            const data = this._restaurantsMapper.toSearchRestaurantArrayDto(restaurants);
            return res.json({
                data,
            });
        } catch (err) {
            next(err);
        }
    }

    @Query(searchRestaurantsV2QueryValidator)
    async handleSearchRestaurantsV2(
        req: Request<any, any, any, SearchRestaurantsV2QueryDto>,
        res: Response<ApiResultV2<Partial<IRestaurant>[], { pagination: { total: number } }>>,
        next: NextFunction
    ) {
        try {
            const { text, fields, limit, offset, active } = req.query;
            const { user } = req;

            const result = await this._searchRestaurantsV2UseCase.execute({
                text,
                fields,
                limit,
                offset,
                active,
                userId: user._id,
            });

            return res.json({
                data: result.data,
                metadata: { pagination: { total: result.total } },
            });
        } catch (err) {
            next(err);
        }
    }

    @Query(getAllRestaurantsQueryValidator)
    async handleGetAllRestaurants(
        req: Request<any, any, any, GetAllRestaurantsQueryDto>,
        res: Response<ApiResultV2<any[], { pagination: { total: number } }>>,
        next: NextFunction
    ) {
        try {
            const { fields, active } = req.query;

            const filter: any = {};
            if (active !== undefined) {
                filter.active = active;
            }

            const restaurants = await this._restaurantsUseCases.getAllRestaurants({
                fields: fields ?? [],
                active,
            });

            return res.json({
                data: restaurants,
            });
        } catch (err) {
            next(err);
        }
    }

    async handleGetRestaurantsWithoutSticker(_req: Request, res: Response<ApiResultV2<RestaurantWithoutStickerDto[]>>, next: NextFunction) {
        try {
            const restaurants = await this._getRestaurantsWithoutStickerUseCase.execute();
            return res.json({
                data: restaurants,
            });
        } catch (err) {
            next(err);
        }
    }

    @Query(getRestaurantsFromProviderQueryValidator)
    async handleGetRestaurantsFromProvider(
        req: Request<any, any, any, GetRestaurantsFromProviderQueryDto>,
        res: Response<ApiResultV2<GetRestaurantsFromProviderResponseDto>>,
        next: NextFunction
    ) {
        try {
            const query = req.query;
            const result = await this._getRestaurantsFromProviderUseCase.execute(query);
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    async handleAddRestaurantForUser(req: Request<CheckRestaurantIdInParamsDto>, res: Response<ApiResult>, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            const userId = req.user._id;
            await this._restaurantsUseCases.addRestaurantForUser(userId, restaurantId);
            return res.json({ msg: 'Success' });
        } catch (err) {
            next(err);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    async handleUpdateRestaurantCompletionScore(
        req: Request<CheckRestaurantIdInParamsDto>,
        res: Response<ApiResultV2<number>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;

            const score = await this._computeRestaurantCompletionScoreService.execute({
                restaurantId,
                shouldUpdate: true,
            });

            return res.json({ data: score });
        } catch (err) {
            next(err);
        }
    }

    @Params(addRestaurantForUserWithEmailParamsValidator)
    @Body(addRestaurantForUserWithEmailBodyValidator)
    async handleAddRestaurantForUserWithEmail(
        req: RequestWithPermissions<AddRestaurantForUserWithEmailParamsDto, any, AddRestaurantForUserWithEmailBodyDto>,
        res: Response<ApiResult<AddUserToRestaurantWithEmailResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId, userId } = req.params;
            const { caslRole } = req.body;

            assert(req.userRestaurantsAbility, 'User restaurants ability is not defined');
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.USER_RESTAURANT, { restaurantId })
            );
            const newUserRestaurant = await this._restaurantsUseCases.addRestaurantForUser(userId, restaurantId, caslRole);
            await this._addRestaurantToUserConfigurationUseCase.execute({ userId, restaurantId });
            const data = this._restaurantsMapper.toAddRestaurantForUserDto(newUserRestaurant);
            return res.json({
                data,
            });
        } catch (err) {
            return next(err);
        }
    }

    @Params(getRestaurantsForUserWithEmailParamsValidator)
    async handleGetRestaurantsForUserWithEmail(
        req: RequestWithPermissions<GetRestaurantsForUserWithEmailParamsDto>,
        res: Response<ApiResultV2<GetUserWithEmailRestaurantsResponseDto[]>>,
        next: NextFunction
    ) {
        try {
            const { userId } = req.params;

            assert(req.userRestaurantsAbility, 'User restaurants ability is not defined');
            const data = await this._getRestaurantsForUserUseCase.execute(userId, req.userRestaurantsAbility);
            return res.json({
                data,
            });
        } catch (err) {
            return next(err);
        }
    }

    @Params(addRestaurantsForUserWithEmailParamsValidator)
    @Body(addRestaurantsForUserWithEmailBodyValidator)
    async handleAddRestaurantsForUserWithEmail(
        req: RequestWithPermissions<AddRestaurantsForUserWithEmailParamsDto, any, AddRestaurantsForUserWithEmailBodyDto>,
        res: Response<ApiResultV2<AddUserToRestaurantWithEmailResponseDto[]>>,
        next: NextFunction
    ) {
        try {
            const { userId } = req.params;
            const { caslRole, restaurantIds } = req.body;

            assert(req.userRestaurantsAbility, 'User restaurants ability is not defined');
            const data = await this._addRestaurantsForUserUseCase.execute({
                userId,
                restaurantIds,
                caslRole,
                ability: req.userRestaurantsAbility,
            });
            return res.json({
                data,
            });
        } catch (err) {
            return next(err);
        }
    }

    @Params(updateRestaurantsForUserWithEmailParamsValidator)
    @Body(updateRestaurantsForUserWithEmailBodyValidator)
    async handleUpdateRestaurantsForUserWithEmail(
        req: RequestWithPermissions<updateRestaurantsForUserWithEmailParamsDto, any, updateRestaurantsForUserWithEmailBodyDto>,
        res: Response<ApiResultV2<UpdateRestaurantsForUserDto>>,
        next: NextFunction
    ) {
        try {
            const { userId } = req.params;
            const { caslRole, restaurantIds } = req.body;

            assert(req.userRestaurantsAbility, 'User restaurants ability is not defined');
            const updateSuccessStatus = await this._updateRestaurantsForUserUseCase.execute({
                userId,
                restaurantIds,
                caslRole,
                ability: req.userRestaurantsAbility,
            });
            return res.json({
                data: updateSuccessStatus,
            });
        } catch (err) {
            return next(err);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    async handleUpdateUserRestaurants(req: Request<CheckRestaurantIdInParamsDto>, res: Response<ApiResult<void>>, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            const { usersIds } = req.body;
            await this._userRestaurantsUseCases.updateUserRestaurants(restaurantId, usersIds);
            return res.status(204).end();
        } catch (err) {
            return next(err);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    async handleRemoveRestaurantForUser(req: Request<CheckRestaurantIdInParamsDto>, res: Response<ApiResult<void>>, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            const userId = req.user._id;
            await this._restaurantsUseCases.removeRestaurantForUser(toDbId(userId).toString(), toDbId(restaurantId).toString());
            return res.json({ msg: 'Success' });
        } catch (err) {
            next(err);
        }
    }

    @Body(removeUserRestaurantsBodyValidator)
    async handleRemoveUserRestaurants(
        req: Request<any, any, RemoveUserRestaurantsBodyDto>,
        res: Response<ApiResult<void>>,
        next: NextFunction
    ) {
        try {
            const { restaurantsIds } = req.body;
            const userId = toDbId(req.user._id);
            await this._userRestaurantsRepository.deleteMany({ filter: { userId, restaurantId: { $in: toDbIds(restaurantsIds) } } });

            return res.json({ msg: 'Success' });
        } catch (err) {
            return next(err);
        }
    }

    @Body(createRestaurantBodyValidator)
    async handleCreateRestaurant(
        req: Request<any, any, CreateRestaurantBodyDto>,
        res: Response<ApiResult<CreateRestaurantResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { _id: userId } = req.user;
            const response = await this._createRestaurantUseCase.execute({ ...req.body, userId: userId.toString() });
            return res.json({
                data: response,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    async handleGetRestaurantById(req: Request<CheckRestaurantIdInParamsDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            const restaurant = await this._restaurantsRepository.getRestaurantByIdPopulated(restaurantId);
            return res.json({ data: restaurant });
        } catch (err) {
            return next(err);
        }
    }

    @Params(updateRestaurantParamsValidator)
    @Body(updateRestaurantBodyValidator)
    async handleUpdateRestaurant(
        req: RequestWithPermissions<UpdateRestaurantParamsDto, never, UpdateRestaurantBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId: restaurantInputId } = req.params;
            const { duplicatedFromRestaurantId, ...params } = req.body;
            const restaurantId = toDbId(restaurantInputId);
            const userRestaurantsAbility = req.userRestaurantsAbility;
            assert(userRestaurantsAbility, 'User restaurants ability is not defined');

            const restaurant = await this._restaurantsRepository.findOne({ filter: { _id: restaurantId }, options: { lean: true } });

            if (
                req.body?.address &&
                'administrativeArea' in req.body?.address &&
                (!restaurant?.address || !Object.keys(restaurant.address).includes('administrativeArea'))
            ) {
                delete params.address?.administrativeArea;
            }
            // should match handleSaveInformationUpdateData validation in information-updates.controller.ts
            Object.keys(omit(params, 'commentsLastUpdate', 'logo', 'logoChanged', 'coverChanged', 'cover') ?? {}).forEach((key) => {
                ForbiddenError.from(userRestaurantsAbility).throwUnlessCan(
                    CaslAction.UPDATE,
                    subject(CaslSubject.RESTAURANT, { _id: restaurantId }),
                    key
                );
            });

            if (params.currentState) {
                // sometimes we receive a partial currentState, we need to update only the fields that are present
                for (const [key, value] of Object.entries(params.currentState)) {
                    params[`currentState.${key}`] = value;
                }

                delete params.currentState;
            }

            if (params.descriptions && duplicatedFromRestaurantId) {
                params.descriptions = params.descriptions.map((description) => ({ ...description, duplicatedFromRestaurantId }));
            }

            await this._restaurantsRepository.findOneAndUpdate({ filter: { _id: restaurantId }, update: omit(params, 'attributeList') });
            if (params.attributeList) {
                await this._updateRestaurantAttributesUseCase.execute(params.attributeList, restaurantId, duplicatedFromRestaurantId);
            }
            const populatedRestaurant = await this._restaurantsRepository.getRestaurantByIdPopulated(restaurantInputId);

            if (params.name) {
                await this._updateRestaurantNameService.execute({
                    restaurant: restaurant ?? undefined,
                    updatedName: params.name,
                });
            }

            void this._computeRestaurantCompletionScoreService
                .execute({ restaurantId: restaurantId.toString(), shouldUpdate: true })
                .catch((error) => {
                    logger.error('Error computing completion score', { error, restaurantId });
                });

            return res.json({
                msg: 'Restaurant updated',
                data: populatedRestaurant,
            });
        } catch (err) {
            return next(err);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    async handleFetchPlatformAndUpsertRestaurant(req: Request<CheckRestaurantIdInParamsDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            const params = req.body;

            const updatedRestaurant = await this._restaurantsUseCases.upsertRestaurantFromPlatform(params, toDbId(restaurantId));
            return res.json({ msg: 'Restaurant updated', data: updatedRestaurant });
        } catch (err) {
            next(err);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    @Body(updateRestaurantActiveBodyValidator)
    async handleUpdateRestaurantActive(
        req: Request<CheckRestaurantIdInParamsDto, never, UpdateRestaurantActiveBodyDto>,
        res: Response<ApiResultV2<IRestaurant>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { active } = req.body;
            const updatedRestaurant = await this._updateRestaurantActiveUseCase.execute(restaurantId, active);

            assert(updatedRestaurant, 'Did not find restaurant');

            return res.json({
                data: updatedRestaurant,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(adminUpdateRestaurantParamsValidator)
    @Body(adminUpdateRestaurantBodyValidator)
    async handleAdminUpdateRestaurant(
        req: Request<AdminUpdateRestaurantParamsDto, never, AdminUpdateRestaurantBodyDto>,
        res: Response<ApiResultV2<IRestaurant>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { _id: userId } = req.user;
            const update = req.body;

            const updatedRestaurant = await this._restaurantsUseCases.adminUpdateRestaurant(restaurantId, update, userId.toString());

            assert(updatedRestaurant, 'Did not find restaurant');

            return res.json({
                data: updatedRestaurant,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(yextToggleParamsValidator)
    async handleActivateYextLocation(
        req: Request<ActivateYextParamsDto, never, never>,
        res: Response<ApiResultV2<IRestaurant>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { _id: userId } = req.user;
            const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);
            assert(restaurant, 'Did not find restaurant');

            const updatedRestaurant = await this._yextActivateLocationUseCase.execute(restaurant, userId.toString());
            assert(updatedRestaurant, 'Did not find restaurant');

            return res.json({
                data: updatedRestaurant,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(yextToggleParamsValidator)
    async handleDeactivateYextLocation(
        req: Request<DeactivateYextParamsDto, never, never>,
        res: Response<ApiResultV2<IRestaurant>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { _id: userId } = req.user;

            await this._yextDeleteLocationUseCase.execute(restaurantId);

            const updatedRestaurant = await this._restaurantsUseCases.adminUpdateRestaurant(
                restaurantId,
                { isYextActivated: false },
                userId.toString()
            );
            assert(updatedRestaurant, 'Did not find restaurant');

            return res.json({
                data: updatedRestaurant,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateRestaurantOrganizationParamsValidator)
    @Body(updateRestaurantOrganizationRequestBodyValidator)
    async handleUpdateRestaurantOrganization(
        req: Request<UpdateRestaurantOrganizationParamsDto, never, UpdateRestaurantOrganizationRequestBodyDto>,
        res: Response<ApiResultV2<undefined>>,
        next: NextFunction
    ) {
        try {
            await this._updateRestaurantOrganizationUseCase.execute(req.params.restaurantId, req.body.organizationId);

            return res.json({
                data: undefined,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(createPlatformAccessParamsValidator)
    @Body(createPlatformAccessBodyValidator)
    async handleCreatePlatformAccess(
        req: Request<CreatePlatformAccessParamsDto, never, CreatePlatformAccessBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const platformAccess = req.body.data;
            const { user } = req;
            const access = new Access(platformAccess as IRestaurantAccess);
            const updatedPlatformAccess = await this._createPlatformAccessUseCase.execute(restaurantId, {
                platformAccess: access,
                user,
            });
            return res.status(200).json({ msg: 'Success', data: updatedPlatformAccess });
        } catch (err) {
            next(err);
        }
    }

    @Params(updatePlatformAccessStatusParamsValidator)
    @Body(updatePlatformAccessStatusBodyValidator)
    async handleUpdatePlatformAccessStatus(
        req: Request<UpdatePlatformAccessStatusParamsDto, never, UpdatePlatformAccessStatusBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            await this._updatePlatformAccessStatusUseCase.execute({
                ...req.body,
                ...req.params,
            });
            return res.status(200).json({});
        } catch (err) {
            next(err);
        }
    }

    @Params(handleValidatePlatformAccessParamsValidator)
    @Body(handleValidatePlatformAccessBodyValidator)
    async handleValidatePlatformAccess(
        req: Request<HandleValidatePlatformAccessParamsDto, never, HandleValidatePlatformAccessBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { platformKey, socialId } = req.body;

            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                options: { lean: true },
            });

            assert(restaurant, 'Restaurant not found in db');

            const result = await this._validatePlatformAccessUseCase.execute({
                platformKey,
                socialId,
            });

            return res.status(200).json({ msg: 'Success', data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(handleShowPlatformAccessParamsValidator)
    async handleShowPlatformAccess(req: Request<HandleShowPlatformAccessParamsDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantId, platformKey } = req.params;
            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                options: { lean: true },
            });

            if (!restaurant) {
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found' });
            }
            const password = await this._restaurantsUseCases.getPlatformAccess(toDbId(restaurantId), platformKey);

            if (typeof password !== 'string' && password.error) {
                throw new MalouError(MalouErrorCode.PLATFORM_INVALID_PASSWORD, {
                    metadata: password.error,
                });
            }

            return res.status(200).json({ msg: 'Success', data: { key: platformKey, password } });
        } catch (err) {
            next(err);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    async handleRunBookmarkedPostJob(req: Request<CheckRestaurantIdInParamsDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            const {
                post: { url, carouselUrls, socialId },
            } = req.body;
            let urls = [url];
            if (carouselUrls?.length > 0) {
                urls = urls.concat(carouselUrls.map((cUrl) => cUrl.url));
            }

            await this._agendaSingleton.now(AgendaJobName.SAVE_BOOKMARKED_POST, { urls, socialId, restaurantId });

            return res.status(200).json({ msg: 'Success' });
        } catch (err) {
            next(err);
        }
    }

    @Params(handleGetOrganizationRestaurantsParamsValidator)
    async handleGetOrganizationRestaurants(req: Request<HandleGetOrganizationRestaurantsParamsDto>, res: Response, next: NextFunction) {
        try {
            const { organizationId } = req.params;
            const { fields } = req.query;
            const restaurants = await this._restaurantsRepository.find({
                filter: { organizationId: toDbId(organizationId), active: true },
                projection: projectionToArray(fields as string | string[]).reduce((acc, field) => ({ ...acc, [field]: 1 }), {}),
            });
            return res.status(200).json({ data: restaurants, msg: 'organization restaurants' });
        } catch (err) {
            next(err);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    async handleGetRestaurantCurrentState(
        req: Request<CheckRestaurantIdInParamsDto>,
        res: Response<ApiResult<GetRestaurantCurrentStateResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;

            const restaurantCurrentState = await this._restaurantsRepository.findOneOrFail({
                filter: { _id: toDbId(restaurantId), active: true },
                projection: { currentState: 1 },
                options: { lean: true },
            });

            const restaurantCurrentStateResponseDto = this._restaurantsMapper.toRestaurantCurrentStateDto(restaurantCurrentState);
            assert(restaurantCurrentStateResponseDto, 'Did not find restaurant current state');

            return res.status(200).json({ data: restaurantCurrentStateResponseDto });
        } catch (err) {
            next(err);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    async getManagers(req: Request<CheckRestaurantIdInParamsDto>, res: Response<ApiResult<GetManagersResponseDto[]>>, next: NextFunction) {
        try {
            const { restaurantId } = req.params;

            const managers = await this._restaurantsUseCases.getManagersForRestaurant(toDbId(restaurantId));

            return res.status(200).json({ data: managers });
        } catch (err) {
            next(err);
        }
    }

    @Query(getRestaurantsByIdsQueryValidator)
    async handleGetRestaurantsByIds(
        req: Request<any, any, any, GetRestaurantsByIdsQueryDto>,
        res: Response<ApiResultV2<IRestaurant[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantIds } = req.query;
            const restaurants = await this._restaurantsUseCases.getRestaurantsByIds(toDbIds(restaurantIds));
            return res.status(200).json({ data: restaurants });
        } catch (error) {
            next(error);
        }
    }

    @Params(checkRestaurantIdInParamsValidator)
    @Body(duplicateSpecialHoursBodyValidator)
    async handleDuplicateSpecialHours(
        req: Request<CheckRestaurantIdInParamsDto, never, DuplicateSpecialHoursBodyDto>,
        res: Response<ApiResultV2<{ success: boolean }>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { restaurantIds, specialHoursToDuplicate } = req.body;
            const data = await this._duplicateSpecialHoursUseCase.execute(restaurantId, specialHoursToDuplicate, restaurantIds);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateRestaurantCalendarEventsCountryParamsValidator)
    @Body(updateRestaurantCalendarEventsCountryBodyValidator)
    async handleUpdateRestaurantCalendarEventsCountry(
        req: Request<UpdateRestaurantCalendarEventsCountryParamsDto, never, UpdateRestaurantCalendarEventsCountryBodyDto>,
        res: Response<void>,
        next: NextFunction
    ) {
        try {
            await this._updateRestaurantCalendarEventsCountryUseCase.execute(req.params.restaurantId, req.body.calendarEventsCountry);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    }

    @Params(getStoreLocatorOrganizationRestaurantsParamsValidator)
    async handleGetStoreLocatorOrganizationRestaurants(
        req: Request<GetStoreLocatorOrganizationRestaurantsParamsDto>,
        res: Response<ApiResultV2<StoreLocatorOrganizationRestaurantDto[]>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const restaurants = await this._getStoreLocatorOrganizationRestaurantsUseCase.execute(organizationId);
            return res.status(200).json({ data: restaurants });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateSubscriptionsProviderLocationParamsValidator)
    @Body(updateSubscriptionsProviderLocationBodyValidator)
    async handleUpdateSubscriptionsProviderLocation(
        req: Request<UpdateSubscriptionsProviderLocationParamsDto, never, UpdateSubscriptionsProviderLocationBodyDto>,
        res: Response<ApiResult<UpdateSubscriptionsProviderLocationResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { platformKey, newSubscriptionsProviderId } = req.body;

            await this._updateSubscriptionsProviderLocationUseCase.execute({
                restaurantId,
                platformKey,
                newSubscriptionsProviderId,
            });

            return res.json({ msg: 'success' });
        } catch (err) {
            next(err);
        }
    }
}
