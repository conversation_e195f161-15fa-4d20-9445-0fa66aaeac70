import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { Role } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { getDefaultUser } from ':modules/users/tests/user.builder';
import { UsersRepository } from ':modules/users/users.repository';
import { CreateNewOrganizationUseCase } from ':modules/webhooks/malou/use-cases/create-new-organization.use-case';
import { NewOrganizationEvent } from ':modules/webhooks/malou/validators/new-organization.validators';

const event: NewOrganizationEvent = {
    organizationName: 'Test Organization',
    organizationProviderId: 'org-provider-123',
    principalUserEmail: '<EMAIL>',
    principalUserProviderId: 'user-provider-456',
};

let createNewOrganizationUseCase: CreateNewOrganizationUseCase;

describe('CreateNewOrganizationUseCase', () => {
    beforeEach(() => {
        container.clearInstances();

        registerRepositories(['OrganizationsRepository', 'UsersRepository']);
    });

    describe('execute', () => {
        it('should successfully create organization and user when no duplicates exist', async () => {
            const organizationsRepository = container.resolve(OrganizationsRepository);
            const usersRepository = container.resolve(UsersRepository);

            const mockSubscriptionsProvider = {
                updateSubscriptionsProviderLocation: jest.fn().mockResolvedValue(undefined),
            };
            container.register('SubscriptionsProvider', { useValue: mockSubscriptionsProvider });

            const testCase = new TestCaseBuilderV2<'organizations' | 'users'>({
                seeds: {
                    organizations: {
                        data() {
                            return [];
                        },
                    },
                    users: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            createNewOrganizationUseCase = container.resolve(CreateNewOrganizationUseCase);

            await createNewOrganizationUseCase.execute(event);

            // Verify organization was created
            const organizations = await organizationsRepository.find({
                filter: { subscriptionsProviderId: event.organizationProviderId },
                options: { lean: true },
            });
            expect(organizations).toHaveLength(1);
            expect(organizations[0].name).toBe(event.organizationName);
            expect(organizations[0].subscriptionsProviderId).toBe(event.organizationProviderId);

            // Verify user was created
            const users = await usersRepository.find({
                filter: { subscriptionsProviderId: event.principalUserProviderId },
                options: { lean: true },
            });
            expect(users).toHaveLength(1);
            expect(users[0].email).toBe(event.principalUserEmail.toLowerCase());
            expect(users[0].subscriptionsProviderId).toBe(event.principalUserProviderId);
            expect(users[0].role).toBe(Role.MALOU_BASIC);
            expect(users[0].organizationIds).toHaveLength(1);
            expect(users[0].organizationIds[0].toString()).toBe(organizations[0]._id.toString());
        });

        it('should throw error when organization with same subscriptionsProviderId already exists', async () => {
            const testCase = new TestCaseBuilderV2<'organizations' | 'users'>({
                seeds: {
                    organizations: {
                        data() {
                            return [
                                getDefaultOrganization()
                                    .name('Existing Organization')
                                    .subscriptionsProviderId(event.organizationProviderId)
                                    .build(),
                            ];
                        },
                    },
                    users: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            await expect(createNewOrganizationUseCase.execute(event)).rejects.toThrow();
        });

        it('should throw error when user with same subscriptionsProviderId already exists and not create anything due to transaction rollback', async () => {
            const organizationsRepository = container.resolve(OrganizationsRepository);
            const usersRepository = container.resolve(UsersRepository);

            const testCase = new TestCaseBuilderV2<'organizations' | 'users'>({
                seeds: {
                    organizations: {
                        data() {
                            return [];
                        },
                    },
                    users: {
                        data() {
                            return [
                                getDefaultUser()
                                    .email('<EMAIL>')
                                    .subscriptionsProviderId(event.principalUserProviderId)
                                    .organizationIds([newDbId()])
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            // Count initial records
            const initialOrganizationsCount = await organizationsRepository.countDocuments({ filter: {} });
            const initialUsersCount = await usersRepository.countDocuments({ filter: {} });

            await expect(createNewOrganizationUseCase.execute(event)).rejects.toThrow();

            // Verify no new records were created due to transaction rollback
            const finalOrganizationsCount = await organizationsRepository.countDocuments({ filter: {} });
            const finalUsersCount = await usersRepository.countDocuments({ filter: {} });

            expect(finalOrganizationsCount).toBe(initialOrganizationsCount);
            expect(finalUsersCount).toBe(initialUsersCount);

            // Verify the specific organization was not created
            const organizations = await organizationsRepository.find({
                filter: { subscriptionsProviderId: event.organizationProviderId },
                options: { lean: true },
            });
            expect(organizations).toHaveLength(0);
        });

        it('should rollback organization and user creation when subscriptions provider update fails', async () => {
            const organizationsRepository = container.resolve(OrganizationsRepository);
            const usersRepository = container.resolve(UsersRepository);

            const testCase = new TestCaseBuilderV2<'organizations' | 'users'>({
                seeds: {
                    organizations: {
                        data() {
                            return [];
                        },
                    },
                    users: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();
            const mockSubscriptionsProvider = {
                updateSubscriptionsProviderLocation: jest.fn().mockRejectedValue(new Error('Subscriptions provider API failed')),
            };
            container.register('SubscriptionsProvider', { useValue: mockSubscriptionsProvider });

            createNewOrganizationUseCase = container.resolve(CreateNewOrganizationUseCase);

            await expect(createNewOrganizationUseCase.execute(event)).rejects.toThrow();

            // Verify no new records were created due to transaction rollback
            const organizations = await organizationsRepository.find({
                filter: { subscriptionsProviderId: event.organizationProviderId },
                options: { lean: true },
            });
            expect(organizations).toHaveLength(0);

            const users = await usersRepository.find({
                filter: { subscriptionsProviderId: event.principalUserProviderId },
                options: { lean: true },
            });
            expect(users).toHaveLength(0);
        });
    });
});
