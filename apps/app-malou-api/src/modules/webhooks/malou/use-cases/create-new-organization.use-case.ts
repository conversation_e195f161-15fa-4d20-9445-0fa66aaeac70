import { inject, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { Role, UserCaslRole } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { SubscriptionsProvider } from ':modules/restaurants/services/subscriptions.provider.interface';
import { UsersRepository } from ':modules/users/users.repository';
import { NewOrganizationEvent } from ':modules/webhooks/malou/validators/new-organization.validators';

const defaultPassword = 'ad126d5f6bb146d2f581'; // The user will be asked to change it on first login

@singleton()
export class CreateNewOrganizationUseCase {
    constructor(
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _usersRepository: UsersRepository,
        @inject(InjectionToken.SubscriptionsProvider) private readonly _subscriptionsProvider: SubscriptionsProvider
    ) {}

    async execute(event: NewOrganizationEvent): Promise<void> {
        let createdOrganization: any = null;
        let createdUser: any = null;

        try {
            createdOrganization = await this._organizationsRepository.create({
                data: {
                    name: event.organizationName,
                    subscriptionsProviderId: event.organizationProviderId,
                },
            });

            createdUser = await this._usersRepository.create({
                data: {
                    email: event.principalUserEmail.toLowerCase(),
                    password: defaultPassword,
                    subscriptionsProviderId: event.principalUserProviderId,
                    organizationIds: [createdOrganization._id],
                    role: Role.MALOU_BASIC,
                    caslRole: UserCaslRole.OWNER,
                },
            });
            await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                subscriptionsProviderLocationId: event.organizationProviderId,
                malouRestaurantId: createdOrganization._id.toString(),
            });
        } catch (err) {
            if (createdOrganization) {
                await this._organizationsRepository.deleteOne({
                    filter: { _id: toDbId(createdOrganization._id.toString()) },
                });
            }
            if (createdUser) {
                await this._usersRepository.deleteOne({
                    filter: { _id: toDbId(createdUser._id.toString()) },
                });
            }
            throw err;
        }
    }
}
