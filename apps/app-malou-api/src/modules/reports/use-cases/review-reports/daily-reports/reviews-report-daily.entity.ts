import { DateTime } from 'luxon';

import { DailyReportProps } from '@malou-io/package-dto';

import { ReportEmail } from ':modules/reports/report-email.entity';

export class DailyReviewsReport extends ReportEmail<DailyReportProps> {
    getReportEmailSubject(): string {
        const count = this.getNumberOfReviews();
        const restaurantName = this.hasManyRestaurants()
            ? this._translator.fromLang({ lang: this._getLang() }).common.your_locations().toLowerCase()
            : (this.getEmailProps().concernedRestaurants[0]?.name ??
              this._translator.fromLang({ lang: this._getLang() }).common.your_locations().toLowerCase());

        if (count > 1) {
            return this._translator.fromLang({ lang: this._getLang() }).reports.reviews_report.daily.subject.many({
                count,
                restaurantName,
            });
        }
        return this._translator.fromLang({ lang: this._getLang() }).reports.reviews_report.daily.subject.one({
            count,
            restaurantName,
        });
    }

    hasManyRestaurants(): boolean {
        return this.getEmailProps().concernedRestaurants.length > 1;
    }

    getNumberOfReviews(): number {
        return isNaN(this.getEmailProps().reviewsStats.stats.rating.totalRated)
            ? 0
            : this.getEmailProps().reviewsStats.stats.rating.totalRated;
    }

    getPdfFileName(): string {
        return `${this._translator.fromLang({ lang: this._getLang() }).reports.reviews_report.daily.title()}-${DateTime.now().toFormat(
            'dd-MM-yyyy',
            {
                locale: this.getEmailProps().locale,
            }
        )}.pdf`;
    }
}
