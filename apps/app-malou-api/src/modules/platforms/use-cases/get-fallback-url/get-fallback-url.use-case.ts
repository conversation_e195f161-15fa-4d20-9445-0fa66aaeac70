import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { getPlatformDefinition, PlatformKey } from '@malou-io/package-utils';

import PlatformsRepository from ':modules/platforms/platforms.repository';

@singleton()
export class GetFallbackUrlUseCase {
    constructor(private readonly _platformsRepository: PlatformsRepository) {}

    async execute(platformKey: PlatformKey, restaurantId: string): Promise<string | null> {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, platformKey);
        const socialId = platform?.socialId;
        if (!socialId) {
            return null;
        }
        if (platformKey === PlatformKey.SEVENROOMS) {
            const platformDefinition = getPlatformDefinition(platformKey);
            assert(platformDefinition, `Platform definition not found for key: ${platformKey}`);
            return platformDefinition.bizUrl + socialId;
        }
        return null;
    }
}
