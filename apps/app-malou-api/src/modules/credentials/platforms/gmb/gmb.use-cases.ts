import axios, { AxiosError, AxiosResponse, Method } from 'axios';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { EntityRepository, ICredential, ID, IGmbCredential, IUserWithOrganizations } from '@malou-io/package-models';
import {
    GmbErrorCode,
    GmbNotificationType,
    MalouErrorCode,
    MediaCategory,
    PlatformKey,
    retry,
    RetryError,
    TimeInMilliseconds,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { IPlatformCredentialsUseCases } from ':modules/credentials/credentials.interface';
import { GmbCredentialsRepository } from ':modules/credentials/platforms/gmb/gmb.repository';
import { Account, FetchedAccountLocation, PostInsightsBody, PostInsightsResponse } from ':modules/credentials/platforms/gmb/interfaces';
import {
    GbmAttributesList,
    GmbDailyMetric,
    GmbMediaItemOutput,
    GmbMultiDailyMetricTimeSeries,
} from ':modules/platforms/platforms/gmb/gmb.types';
import { GmbLocalPost } from ':modules/posts/platforms/gmb/gmb-post.interface';
import { GmbReplyPayload } from ':modules/reviews/platforms/gmb/interface';
import { gmbOAuth2Client } from ':providers/google/gmb.oauth2.client';

const MAX_COUNT = 100;
@singleton()
export class GmbApiProviderUseCases implements IPlatformCredentialsUseCases {
    constructor(private _gmbCredentialsRepository: GmbCredentialsRepository) {}

    create(): Promise<ICredential> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'Method not implemented.',
        });
    }

    patch(): Promise<ICredential> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'Method not implemented.',
        });
    }

    getToken = (code: string) => gmbOAuth2Client.getToken(code);

    getTokenInfo = (accessToken: string) => gmbOAuth2Client.getTokenInfo(accessToken);

    /**
     *
     * Fetch locations associated to gmb account
     * GMB: https://developers.google.com/my-business/reference/rest/v4/accounts.locations/list (deprecated)
     * Replacement: https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations#list
     */
    fetchAccountLocations = async (accountId: string, credentialId: string): Promise<FetchedAccountLocation[]> => {
        const readMask =
            'profile,latlng,labels,serviceArea,languageCode,storeCode,name,title,phoneNumbers,categories,storefrontAddress,metadata(placeId,hasGoogleUpdated),openInfo,serviceItems,moreHours,specialHours,regularHours,websiteUri';
        let res = await this._businessInformationApiCallWithRetry({
            credentialId,
            method: 'get',
            url: `accounts/${accountId}/locations`,
            params: { pageSize: 50, readMask },
        });
        let locations = res?.data?.locations || [];
        let count = 0;
        logger.info('[WHILE_TRACKER] - gmb fetch account locations started', { credentialId, accountId });
        while (res?.data?.nextPageToken && count < MAX_COUNT) {
            res = await this._businessInformationApiCallWithRetry({
                credentialId,
                method: 'get',
                url: `accounts/${accountId}/locations`,
                params: { pageToken: res.data.nextPageToken, pageSize: 50, readMask },
            });
            locations = locations.concat(res?.data?.locations || []);
            count += 1;
        }
        logger.info('[WHILE_TRACKER] - gmb fetch account locations ended', { credentialId, accountId });
        return locations;
    };

    /**
     * Fetch accounts associated to gmb credentials
     * GMB: https://developers.google.com/my-business/reference/rest/v4/accounts/list (deprecated)
     * Replacement: https://developers.google.com/my-business/reference/accountmanagement/rest/v1/accounts#list
     */
    fetchAccounts = async (credentialId: string): Promise<Account[]> => {
        const res = await this._businessInformationApiCall({
            credentialId,
            method: 'get',
            url: 'accounts',
        });
        return res?.data?.accounts ?? [];
    };

    /**
     * Get a gmb location
     * GMB: https://developers.google.com/my-business/reference/rest/v4/accounts.locations/get (deprecated)
     * Replacement: https://developers.google.com/my-business/reference/businessinformation/rest/v1/locations#get
     */
    getLocation = async (
        credentialId: string,
        apiEndpointV2: string,
        readMask = 'profile,latlng,labels,serviceArea,languageCode,storeCode,name,title,phoneNumbers,categories,storefrontAddress,metadata,openInfo,serviceItems,moreHours,specialHours,regularHours,websiteUri'
    ): Promise<AxiosResponse<any> | undefined> =>
        this._businessInformationApiCall({
            credentialId,
            method: 'get',
            url: apiEndpointV2,
            params: { readMask },
        });

    /**
     * Get a gmb location
     * GMB: https://developers.google.com/my-business/reference/rest/v4/accounts.locations/get (deprecated)
     * Replacement: https://developers.google.com/my-business/reference/businessinformation/rest/v1/locations#get
     *
     * Attributes are based on category of the location
     * Example: if you are a "Bubble tea store" Google won't return the orderUrl attribute
     * so don't be surprised if you don't see it in the response !!
     */
    getLocationAttributes = async (credentialId: string, apiEndpointV2: string) =>
        this._businessInformationApiCall({
            credentialId,
            method: 'get',
            url: `${apiEndpointV2}/attributes`,
        });

    /**
     * Fetch list of location reviews, including averageRating and totalReviewCount
     * GMB: Returns the paginated list of reviews for the specified location. This operation is only valid if the specified location is verified.
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.reviews/list
     */
    fetchLocationReviews = async (credentialId: string, apiEndpoint: string, pageToken = '') => {
        // pageSize, orderBy are available as params too vic
        const res = await this._v4ApiCall({
            credentialId,
            method: 'get',
            params: { pageToken },
            url: `${apiEndpoint}/reviews`,
        });
        return res?.data;
    };

    /**
     * Fetch ALL location reviews for a given location
     */
    fetchAllLocationReviews = async (credentialId: string, apiEndpoint: string, recentOnly: boolean) => {
        const limitCount = recentOnly ? 1 : 40; // limit to 40 * 50 = 2000 reviews
        let nextPageToken = null;
        let count = 0;
        const reviews: any[] = [];
        logger.info('[WHILE_TRACKER] - gmb fetch all location reviews started', {
            credentialId,
            limitCount,
        });
        do {
            // get all reviews for a location
            const result = await this.fetchLocationReviews(credentialId, apiEndpoint, nextPageToken ?? undefined);
            if ('reviews' in result) {
                nextPageToken = result.nextPageToken || null; // set next page token
                reviews.push(...result.reviews);
            }
            count += 1;
        } while (nextPageToken && count < limitCount);
        logger.info('[WHILE_TRACKER] - gmb fetch all location reviews ended', {
            credentialId,
            limitCount,
            reviewsFetchCount: reviews.length,
        });
        return reviews;
    };

    /**
     * Fetch list of location reviews media
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.media.customers/list
     * @param {string} credentialId
     * @param {string} apiEndpoint
     * @param {string} pageToken
     */
    fetchLocationMedia = async (credentialId, apiEndpoint, pageToken) => {
        const res = await this._v4ApiCall({
            credentialId,
            method: 'get',
            params: { pageToken },
            url: `${apiEndpoint}/media/customers?pageSize=200`,
        });
        return res?.data;
    };

    /**
     * Fetch ALL location reviews media for a given location
     * @param {string} credentialId
     * @param {string} apiEndpoint
     * @param {Boolean} recentOnly
     */
    fetchAllLocationReviewsMedia = async (credentialId, apiEndpoint, recentOnly) => {
        const MIN_PAGE_NUMBER = 2; // gmb does not send media in a specific order we need to fetch a little bit more to be sure to get the latest media
        const MAX_PAGE_NUMBER = 40;
        let nextPageToken = null;
        let count = 0;
        const media: any[] = [];
        logger.info('[WHILE_TRACKER] - gmb fetch all location reviews media started', { credentialId });
        do {
            // get all medias for a location
            const result = await this.fetchLocationMedia(credentialId, apiEndpoint, nextPageToken);
            if ('mediaItems' in result) {
                nextPageToken = result.nextPageToken || null; // set next page token
                media.push(...result.mediaItems);
            }
            count += 1;
        } while (nextPageToken && count < (recentOnly ? MIN_PAGE_NUMBER : MAX_PAGE_NUMBER)); // limit to 40 * 100 = 4000 media
        logger.info('[WHILE_TRACKER] - gmb fetch all location reviews media ended', { credentialId });
        return media;
    };

    /**
     * https://developers.google.com/my-business/content/accept-or-reject-updates
     */
    getReviewUpdateFields = async (
        credentialId: string,
        apiEndpointV2: string,
        readMask: string
    ): Promise<AxiosResponse<any> | undefined> =>
        this._businessNotificationApiCall({
            credentialId,
            method: 'get',
            url: `${apiEndpointV2}:getGoogleUpdated`,
            params: { readMask },
        });

    /**
     * Create / update a review's reply
     * GMB: https://developers.google.com/my-business/reference/rest/v4/accounts.locations.reviews/updateReply
     */
    updateReviewReply = async (credentialId: string, socialId: string, comment: GmbReplyPayload) => {
        try {
            logger.info('[GMB_REVIEWS][REPLY] - Update review reply', { socialId, comment });
            const res = await this._v4ApiCall({
                credentialId,
                method: 'put',
                url: `${socialId}/reply`,
                data: comment,
            });
            return res?.data;
        } catch (error) {
            logger.error('[GMB_REVIEWS][REPLY] - Update review reply error', { socialId, error });
            if (error instanceof MalouError) {
                if (error.metadata?.['status'] === 404) {
                    logger.warn('[GMB_REVIEWS][REPLY] Review not found', { socialId });
                    throw new MalouError(MalouErrorCode.REVIEW_NOT_FOUND, {
                        message: `Review not found`,
                        metadata: { socialId, credentialId, comment },
                    });
                }
            }
            throw error;
        }
    };

    /**
     * Fetch multiple metrics for a location.
     * https://developers.google.com/my-business/reference/performance/rest/v1/locations/fetchMultiDailyMetricsTimeSeries
     */
    fetchMultiDailyMetricsTimeSeries = async (
        credentialId: string,
        apiEndpoint: string,
        startDate: Date,
        endDate: Date,
        metrics: GmbDailyMetric[]
    ): Promise<GmbMultiDailyMetricTimeSeries | undefined> => {
        const [startDateDay, startDateMonth, startDateYear, endDateDay, endDateMonth, endDateYear] = [
            startDate.getDate(),
            startDate.getMonth() + 1,
            startDate.getFullYear(),
            endDate.getDate(),
            endDate.getMonth() + 1,
            endDate.getFullYear(),
        ];
        const [_account, location] = apiEndpoint.split('/locations');
        const queryParams = {
            'dailyRange.start_date.year': startDateYear,
            'dailyRange.start_date.month': startDateMonth,
            'dailyRange.start_date.day': startDateDay,
            'dailyRange.end_date.year': endDateYear,
            'dailyRange.end_date.month': endDateMonth,
            'dailyRange.end_date.day': endDateDay,
        };

        const query = metrics.map((metric) => `dailyMetrics=${metric}`).join('&');
        try {
            const result: AxiosResponse<GmbMultiDailyMetricTimeSeries> | undefined = await this._businessPerformanceApiCall({
                credentialId,
                method: 'get',
                url: `locations${location}:fetchMultiDailyMetricsTimeSeries?${query}`,
                params: queryParams,
            });
            return result?.data;
        } catch (error) {
            console.log(error);
            throw error;
        }
    };

    /**
     * Get all local posts from a location
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.localPosts/list
     */
    getLocationLocalPosts = async (credentialId: string, apiEndpoint: string): Promise<GmbLocalPost<GmbMediaItemOutput>[]> => {
        let nextPageToken = null;
        let count = 0;
        const posts: any[] = [];
        logger.info('[WHILE_TRACKER] - gmb get location local posts started', { credentialId, apiEndpoint });
        do {
            // get all posts for a location
            const result = await this._v4ApiCall({
                credentialId,
                method: 'get',
                params: { pageToken: nextPageToken, pageSize: 100 },
                url: `${apiEndpoint}/localPosts`,
            });
            if ('localPosts' in result?.data) {
                nextPageToken = result?.data?.nextPageToken; // set next page token
                posts.push(...result?.data.localPosts);
            }
            count += 1;
            // i write this comment the 30/10/2023 : nextPageToken doesnt appear in the response, gmb bug ?
        } while (nextPageToken && count < 40); // limit to 40 * 50 = 2000 posts
        logger.info('[WHILE_TRACKER] - gmb get location local posts ended', { credentialId, apiEndpoint });
        return posts;
    };

    /**
     * Get local post from a location
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.localPosts/get
     * @param {string} credentialId
     * @param {string} socialId
     */
    getLocationLocalPost = async (credentialId, socialId) =>
        this._v4ApiCall({
            credentialId,
            method: 'get',
            url: `${socialId}`,
        });

    /**
     * Delete location local post
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.localPosts/delete
     * @param {string} credentialId
     * @param {string} socialId
     */
    deleteLocationLocalPost = async (credentialId, socialId) =>
        this._v4ApiCall({
            credentialId,
            method: 'delete',
            url: socialId,
        });

    /**
     * Create location local post
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.localPosts/create
     * @param {string} credentialId
     * @param {string} apiEndpoint
     * @param {GmbPost} localPost
     */
    createLocationLocalPost = async (credentialId, apiEndpoint, localPost) =>
        this._v4ApiCall({
            credentialId,
            method: 'post',
            url: `${apiEndpoint}/localPosts`,
            data: { ...localPost },
        });

    /**
     * Update location local post
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.localPosts/patch
     * @param {string} credentialId
     * @param {string} localPostId
     * @param {GmbPost} localPost
     */
    updateLocationLocalPost = async (credentialId, localPostId, localPost) => {
        let updateMask = 'summary,callToAction';
        if (localPost.media?.length) {
            updateMask += ',media';
        }
        // todo: can only update post text for now
        return this._v4ApiCall({
            credentialId,
            method: 'patch',
            url: `${localPostId}`,
            params: { updateMask },
            data: { ...localPost },
        });
    };

    /**
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.media/list
     * @param {string} credentialId
     * @param {string} accountId
     * @param {string} locationId
     * @param {Boolean} all
     */
    listLocationMedia = async (credentialId, endpoint, all = true) => {
        let pageToken;
        let medias: any[] = [];
        let count = 0;
        try {
            logger.info('[WHILE_TRACKER] - gmb list location media started', { credentialId, endpoint });
            do {
                const result = await this._v4ApiCall({
                    credentialId,
                    params: { pageToken },
                    method: 'get',
                    url: `${endpoint}/media`,
                });
                assert(result);
                const {
                    data: { mediaItems, nextPageToken },
                } = result;
                if (!all) {
                    medias = medias.concat(
                        mediaItems.filter(
                            (media) =>
                                media.locationAssociation.category === MediaCategory.PROFILE.toUpperCase() ||
                                media.locationAssociation.category === MediaCategory.COVER.toUpperCase()
                        )
                    );
                } else {
                    medias = medias.concat(mediaItems);
                }
                pageToken = nextPageToken;
                count += 1;
            } while (pageToken && count < MAX_COUNT);
            logger.info('[WHILE_TRACKER] - gmb list location media ended', { credentialId, endpoint });

            return new Promise((resolve) => {
                resolve(medias);
            });
        } catch (error) {
            logger.warn('[LIST_LOCATION_MEDIA_ERROR', error);
        }
    };

    /**
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.media/delete
     * @param {string} credentialId
     * @param {Media} media
     */
    deleteLocationMedia = async (credentialId, media) => {
        const res = await this._v4ApiCall({
            credentialId,
            method: 'delete',
            url: `${media.socialId}`,
        });
        return res?.data;
    };

    /**
     * GMB: https://developers.google.com/my-business/reference/rest/v4/attributes/list (deprecated)
     * Replacement: https://developers.google.com/my-business/reference/businessinformation/rest/v1/attributes/list
     * @param {string} credentialId
     * @param {string} apiEndpointV2
     */
    fetchCategoryAttributes = async (credentialId: string, apiEndpointV2: string): Promise<GbmAttributesList> => {
        const res = await this._businessInformationApiCall({
            credentialId,
            method: 'get',
            url: 'attributes',
            params: { parent: apiEndpointV2 },
        });
        return res?.data;
    };

    /**
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.reviews/get
     * @param {string} credentialId
     * @param {Media} apiEndpoint
     */
    fetchReviewReceived = async (credentialId, apiEndpoint) => {
        const res = await this._v4ApiCall({
            credentialId,
            method: 'get',
            url: apiEndpoint,
        });
        return res?.data;
    };

    /**
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.media.customers/get
     * @param {string} credentialId
     * @param {Media} apiEndpoint
     */
    fetchMediaReceived = async (credentialId, apiEndpoint) => {
        const res = await this._v4ApiCall({
            credentialId,
            method: 'get',
            url: apiEndpoint,
        });
        return res?.data;
    };

    /**
     * https://developers.google.com/my-business/reference/rest/v4/accounts.locations.media.customers/get
     */
    getPostsInsights = async (
        credentialId: string,
        apiEndpoint: string,
        postInsightsBody: PostInsightsBody
    ): Promise<PostInsightsResponse> => {
        const res = await this._v4ApiCall({
            credentialId,
            method: 'post',
            url: `${apiEndpoint}/localPosts:reportInsights`,
            data: postInsightsBody,
        });
        return res?.data;
    };

    /**
     * https://developers.google.com/my-business/reference/notifications/rest/v1/accounts/updateNotificationSetting
     * @param {string} credentialId
     * @param {string} account
     */
    patchNotificationSettings = async (credentialId, account) => {
        const res = await this._businessNotificationApiCall({
            credentialId,
            method: 'patch',
            url: `accounts/${account}/notificationSetting`,
            data: {
                name: `accounts/${account}/notificationSetting`,
                pubsubTopic: 'projects/malou-product/topics/gmb_reviews',
                notificationTypes: Object.values(GmbNotificationType),
            },
            params: { updateMask: 'pubsubTopic,notificationTypes' },
        });
        return res?.data;
    };

    /**
     * https://developers.google.com/my-business/reference/notifications/rest/v1/accounts/getNotificationSetting
     * @param {string} credentialId
     * @param {string} account
     */
    getNotificationsSettings = async (credentialId, account) => {
        const res = await this._businessNotificationApiCall({
            credentialId,
            method: 'get',
            url: `accounts/${account}/notificationSetting`,
        });
        return res?.data;
    };

    /**
     * Refresh the associated gmb tokens
     */
    refreshTokens = async (credential: IGmbCredential) => {
        if (!credential.refreshToken) {
            throw new MalouError(MalouErrorCode.CREDENTIALS_GMB_REFRESH_TOKEN_EXPIRED, {
                message: 'Need to reconnect Google My Business from home page',
            });
        }
        const res = await axios
            .post('https://www.googleapis.com/oauth2/v4/token', {
                client_id: Config.platforms.gmb.api.clientId,
                client_secret: Config.platforms.gmb.api.clientSecret,
                refresh_token: credential.refreshToken,
                grant_type: 'refresh_token',
            })
            .catch((e) => this.handleApiCallError(e));
        const tokens = res.data;
        return this._gmbCredentialsRepository.updateGmbCredentialsById(credential._id, {
            accessToken: tokens.access_token,
            scope: tokens.scope,
            tokenType: tokens.token_type,
            expiresIn: tokens.expires_in,
        });
    };

    handleApiCallError = (err: AxiosError<any>) => {
        if (!err.response) {
            throw new MalouError(MalouErrorCode.CREDENTIALS_GMB_API_ERROR, {
                metadata: {
                    err,
                },
            });
        }

        if (err.response.data?.error?.details?.[0]?.reason === GmbErrorCode.PIN_DROP_REQUIRED) {
            throw new MalouError(MalouErrorCode.GMB_PIN_DROP_REQUIRED, {
                metadata: {
                    err,
                },
            });
        }

        if (err.response.data?.error?.details?.[0]) {
            const errorDetails = err.response.data.error.details[0];
            logger.error('[ERROR_GMB_DETAILS]', errorDetails);
            if (errorDetails.field === 'event.schedule.end_date' && errorDetails.message.match(/be in the past/)) {
                throw new MalouError(MalouErrorCode.CREDENTIALS_GMB_POST_SCHEDULE_END_DATE_IN_PAST, {
                    metadata: {
                        err: errorDetails,
                    },
                });
            }
            throw new MalouError(MalouErrorCode.CREDENTIALS_GMB_API_DETAILS_ERROR, {
                metadata: {
                    err: errorDetails,
                },
            });
        }

        if (err.response.data?.error_description) {
            logger.error('[ERROR_GMB_DETAILS]', err.response.data);
            throw new MalouError(MalouErrorCode.CREDENTIALS_GMB_API_DESCRIPTION_ERROR, {
                metadata: {
                    err: err.response.data.error_description,
                },
            });
        }

        // 401 is reserved for deconnection purposes
        const status = err.response.status === 401 ? 400 : err.response.status;
        throw new MalouError(MalouErrorCode.CREDENTIALS_GMB_API_ERROR, {
            message: err.response.statusText,
            metadata: {
                status,
                err,
            },
        });
    };

    /**
     * Hit gmb's api with an authenticated request
     */
    _v4ApiCall = async ({
        credentialId,
        method,
        url,
        params,
        data,
    }: {
        credentialId: string;
        method: Method;
        url: string;
        params?: Object;
        data?: Object;
    }) => {
        try {
            const instance = await this._getV4APIInstance(credentialId);
            return await instance({
                method,
                url,
                params,
                data,
            });
        } catch (err: any) {
            this.handleApiCallError(err);
        }
    };

    _businessInformationApiCallWithRetry = async ({
        credentialId,
        method,
        url,
        params,
        data,
    }: {
        credentialId: string;
        method: Method;
        url: string;
        params?: Object;
        data?: Object;
    }) => {
        const result = await retry(
            async () => {
                return await this._businessInformationApiCall({
                    credentialId,
                    method,
                    url,
                    params,
                    data,
                });
            },
            {
                attempts: 3,
                backoffStrategy: 'exponential',
                minDelayInMs: TimeInMilliseconds.SECOND,
                maxDelayInMs: 5 * TimeInMilliseconds.SECOND,
                shouldRetryError: (error: unknown, attempt: number) => {
                    if (error && typeof error === 'object' && 'response' in error) {
                        const axiosError = error as AxiosError;
                        const status = axiosError.response?.status;

                        // Retry on 5xx server errors, 429 rate limiting, and 503 service unavailable
                        const retryableStatuses = [429, 500, 502, 503, 504];
                        const shouldRetry = status ? retryableStatuses.includes(status) : false;

                        if (shouldRetry) {
                            logger.warn('[GMB_API_RETRY]', {
                                attempt,
                                status,
                                url,
                                credentialId,
                                message: 'Retrying GMB API call due to transient error',
                            });
                        }

                        return shouldRetry;
                    }

                    // Don't retry for other types of errors
                    return false;
                },
            }
        );

        if (result.isOk()) {
            return result.value;
        } else {
            // If retry failed, throw the original error from the last attempt
            const lastError = result.error;
            if (lastError.error === RetryError.STILL_ERROR_AFTER_RETRIES && lastError.lastResult.isErr()) {
                throw lastError.lastResult.error;
            } else if (lastError.error === RetryError.SHOULD_NOT_RETRY_AFTER_ERROR) {
                throw lastError.originalError;
            } else {
                throw new Error('Unexpected retry error');
            }
        }
    };

    _businessInformationApiCall = async ({
        credentialId,
        method,
        url,
        params,
        data,
    }: {
        credentialId: string;
        method: Method;
        url: string;
        params?: Object;
        data?: Object;
    }) => {
        try {
            const instance = await this._getBusinessInformationAPIInstance(credentialId);
            return await instance({
                method,
                url,
                params,
                data,
            });
        } catch (err: any) {
            this.handleApiCallError(err);
        }
    };

    _businessVerificationApiCall = async ({
        credentialId,
        method,
        url,
        params,
        data,
    }: {
        credentialId: string;
        method: Method;
        url: string;
        params?: Object;
        data?: Object;
    }) => {
        try {
            const instance = await this._getBusinessVerificationAPIInstance(credentialId);
            return await instance({
                method,
                url,
                params,
                data,
            });
        } catch (err: any) {
            this.handleApiCallError(err);
        }
    };

    _businessPerformanceApiCall = async ({
        credentialId,
        method,
        url,
        params,
        data,
    }: {
        credentialId: string;
        method: Method;
        url: string;
        params?: Object;
        data?: Object;
    }) => {
        try {
            const instance = await this._getBusinessPerformanceAPIInstance(credentialId);
            return await instance({
                method,
                url,
                params,
                data,
            });
        } catch (err: any) {
            this.handleApiCallError(err);
        }
    };

    /**
     * Hit gmb's api with an authenticated request
     */

    _businessNotificationApiCall = async ({
        credentialId,
        method,
        url,
        params,
        data,
    }: {
        credentialId: string;
        method: Method;
        url: string;
        params?: Object;
        data?: Object;
    }) => {
        try {
            const instance = await this._getBusinessNotificationAPIInstance(credentialId);
            return await instance({
                method,
                url,
                params,
                data,
            });
        } catch (err: any) {
            this.handleApiCallError(err);
        }
    };

    _isTokenStillValid = (credentialUpdatedAtTimeInSeconds: number, credentialExpiresInTimeInSeconds: number) => {
        const nowInSeconds = new Date().getTime() / 1000;
        return nowInSeconds < credentialUpdatedAtTimeInSeconds + credentialExpiresInTimeInSeconds;
    };

    /**
     * Generate an axios instance ready to hit gmb's api with the correct credentials
     */
    _getV4APIInstance = async (credentialId: string) => {
        const credential = await this._getOrRefreshCredential({ credentialId, api: 'businessV4' });

        const axiosInstance = axios.create({
            baseURL: 'https://mybusiness.googleapis.com/v4/',
            headers: { Authorization: `${credential.tokenType} ${credential.accessToken}` },
        });

        return axiosInstance;
    };

    _getBusinessInformationAPIInstance = async (credentialId: string) => {
        const credential = await this._getOrRefreshCredential({ credentialId, api: 'businessInformation' });

        const axiosInstance = axios.create({
            baseURL: 'https://mybusinessbusinessinformation.googleapis.com/v1/',
            headers: { Authorization: `${credential.tokenType} ${credential.accessToken}` },
        });

        return axiosInstance;
    };

    _getBusinessVerificationAPIInstance = async (credentialId: string) => {
        const credential = await this._getOrRefreshCredential({ credentialId, api: 'businessVerification' });

        const axiosInstance = axios.create({
            baseURL: 'https://mybusinessverifications.googleapis.com/v1/',
            headers: { Authorization: `${credential.tokenType} ${credential.accessToken}` },
        });
        return axiosInstance;
    };

    _getBusinessPerformanceAPIInstance = async (credentialId: string) => {
        const credential = await this._getOrRefreshCredential({ credentialId, api: 'businessProfilePerformance' });

        const axiosInstance = axios.create({
            baseURL: 'https://businessprofileperformance.googleapis.com/v1/',
            headers: { Authorization: `${credential.tokenType} ${credential.accessToken}` },
            timeout: 20000, // occasionally google might let these requests hang. We prefer timeout and retry
        });
        return axiosInstance;
    };

    /**
     * Generate an axios instance ready to hit gmb's notifications API with the correct credentials
     * @param {Object} data
     * @param {string} data.credentialId
     */
    _getBusinessNotificationAPIInstance = async (credentialId: string) => {
        const credential = await this._getOrRefreshCredential({ credentialId, api: 'businessNotifications' });

        const axiosInstance = axios.create({
            baseURL: 'https://mybusinessnotifications.googleapis.com/v1/',
            headers: { Authorization: `${credential.tokenType} ${credential.accessToken}` },
        });
        return axiosInstance;
    };

    private async _getOrRefreshCredential({ credentialId, api }: { credentialId: string; api: string }): Promise<IGmbCredential> {
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.CREDENTIALS_MISSING_PARAM, {
                message: 'Missing param',
                metadata: { param: 'credentialId', platformKey: PlatformKey.GMB },
            });
        }

        let credential = await this._gmbCredentialsRepository.getGmbCredentialById(credentialId);
        if (!credential) {
            throw new MalouError(MalouErrorCode.CREDENTIALS_GMB_CREDENTIAL_NOT_FOUND, {
                message: 'Credentials not found',
                metadata: {
                    credentialId,
                    platformKey: PlatformKey.GMB,
                    api,
                },
            });
        }

        const isTokenStillValid = this._isTokenStillValid(credential.updatedAt.getTime() / 1000, credential.expiresIn ?? 0);
        if (!isTokenStillValid) {
            credential = await this.refreshTokens(credential);
        }
        if (!credential) {
            throw new MalouError(MalouErrorCode.CREDENTIALS_GMB_CREDENTIAL_NOT_FOUND, {
                message: 'Credentials not found',
                metadata: {
                    credentialId,
                    platformKey: PlatformKey.GMB,
                    api,
                },
            });
        }

        return credential;
    }

    handleOauthCallBackUseCase =
        (
            gmbApiAdapter: any,
            credentialsRepository: EntityRepository<ICredential>,
            getNewOrganizationIds: (authId: string, userId: ID, organizationId: ID | null, restaurantId: ID) => Promise<ID[]>
        ) =>
        async ({ user, code, restaurantId }: { user: IUserWithOrganizations; code: string; restaurantId: string }) => {
            const autoOrganization = user.organizations?.length === 1 ? user.organizations[0] : null;
            const { tokens } = await gmbApiAdapter.getToken(code);
            const infos = await gmbApiAdapter.getTokenInfo(tokens.access_token);
            const update: { [key: string]: any } = {
                key: PlatformKey.GMB,
                authId: infos.email,
                active: true,
                accessToken: tokens.access_token,
                refreshToken: tokens.refresh_token,
                scope: tokens.scope,
                tokenType: tokens.token_type,
                expiresIn: (tokens.expiry_date - new Date().getTime()) / 1000 || null,
            };
            await credentialsRepository.upsert({
                filter: {
                    key: PlatformKey.GMB,
                    authId: infos.email,
                    active: true,
                },
                update,
            });
            const organizationIds = await getNewOrganizationIds(infos.email, user._id, autoOrganization?._id ?? null, restaurantId);
            const organizationIdsUpdate = {
                $addToSet: {
                    organizationIds: {
                        $each: organizationIds,
                    },
                },
            };
            const credential = await credentialsRepository.findOneAndUpdate({
                filter: {
                    key: PlatformKey.GMB,
                    authId: infos.email,
                    active: true,
                },
                update: organizationIdsUpdate,
            });
            return credential;
        };
}
