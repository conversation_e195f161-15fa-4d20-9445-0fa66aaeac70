import { singleton } from 'tsyringe';

import { DbId, IClient, IPrivateReview, toDbId } from '@malou-io/package-models';
import { ClientSource, ContactMode, MalouErrorCode, PRIVATE_NEGATIVE_REVIEW_PLATFORM_KEY } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import ClientsRepository from ':modules/clients/clients.repository';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';

@singleton()
export class LinkClientToPrivateReviewUseCase {
    constructor(
        private readonly _clientsRepository: ClientsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository
    ) {}

    async execute({
        privateReviewId,
        email,
        clientId,
    }: {
        privateReviewId: string;
        email?: string;
        clientId?: string;
    }): Promise<IPrivateReview> {
        const privateReview = await this._privateReviewsRepository.getPrivateReviewById(privateReviewId);
        if (!privateReview) {
            throw new MalouError(MalouErrorCode.PRIVATE_REVIEW_NOT_FOUND, {
                message: 'Private review not found',
                metadata: { privateReviewId },
            });
        }

        const client = await this._findOrCreateClient({ restaurantId: privateReview.restaurantId, email, clientId });

        const updatedPrivateReview = await this._privateReviewsRepository.findOneAndUpdate({
            filter: { _id: toDbId(privateReviewId) },
            update: {
                clientId: client._id,
            },
            options: { new: true, lean: true },
        });
        if (!updatedPrivateReview) {
            throw new MalouError(MalouErrorCode.PRIVATE_REVIEW_NOT_FOUND, {
                message: 'Private review not found',
                metadata: { privateReviewId },
            });
        }
        return updatedPrivateReview;
    }

    private async _findOrCreateClient({
        restaurantId,
        email,
        clientId,
    }: {
        restaurantId: DbId;
        email?: string;
        clientId?: string;
    }): Promise<IClient> {
        if (clientId) {
            // When private review is left from WOF, we can have a clientId directly
            const clientById = await this._clientsRepository.findOne({ filter: { _id: toDbId(clientId) } });
            if (!clientById) {
                throw new MalouError(MalouErrorCode.CLIENT_NOT_FOUND, {
                    message: 'Client not found',
                    metadata: { clientId },
                });
            }
            await this._updateClient({ id: toDbId(clientId), baseDataUpdate: {} });
            return clientById;
        }

        const clientByEmail = await this._clientsRepository.findOne({ filter: { email, restaurantId } });
        if (clientByEmail) {
            await this._updateClient({ id: clientByEmail._id, baseDataUpdate: { email, restaurantId } });
            return clientByEmail;
        }

        return this._clientsRepository.create({
            data: {
                email,
                restaurantId,
                lastVisitedAt: new Date(),
                accepts: [ContactMode.EMAIL, ContactMode.SMS],
                lastName: 'Client totem',
                source: ClientSource.TOTEMS,
                visitCount: 1,
                reviewsLeft: [
                    {
                        platformKey: PRIVATE_NEGATIVE_REVIEW_PLATFORM_KEY,
                        hasLeftReview: true,
                    },
                ],
            },
        });
    }

    private async _updateClient({ id, baseDataUpdate = {} }: { id: DbId; baseDataUpdate }): Promise<void> {
        await this._clientsRepository.updateOne({
            filter: { _id: id },
            update: {
                $set: { ...baseDataUpdate, lastVisitedAt: new Date(), accepts: [ContactMode.EMAIL, ContactMode.SMS] },
                $inc: { visitCount: 1 },
                $push: { reviewsLeft: { platformKey: PRIVATE_NEGATIVE_REVIEW_PLATFORM_KEY, hasLeftReview: true } },
            },
        });
    }
}
