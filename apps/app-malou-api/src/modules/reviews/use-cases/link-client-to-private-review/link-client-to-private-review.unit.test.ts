import { omit } from 'lodash';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { ClientSource, ContactMode, MalouErrorCode, PlatformKey, PRIVATE_NEGATIVE_REVIEW_PLATFORM_KEY } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import ClientsRepository from ':modules/clients/clients.repository';
import { getDefaultClient } from ':modules/clients/tests/clients.builder';
import { getDefaultPrivateReview } from ':modules/reviews/tests/private-reviews.builder';
import { LinkClientToPrivateReviewUseCase } from ':modules/reviews/use-cases/link-client-to-private-review/link-client-to-private-review.use-case';

let linkClientToPrivateReviewUseCase: LinkClientToPrivateReviewUseCase;
let clientsRepository: ClientsRepository;

describe('LinkClientToPrivateReviewUseCase', () => {
    beforeAll(() => {
        container.clearInstances();
        registerRepositories(['ClientsRepository', 'PrivateReviewsRepository']);
        linkClientToPrivateReviewUseCase = container.resolve(LinkClientToPrivateReviewUseCase);
        clientsRepository = container.resolve(ClientsRepository);
    });

    describe('execute', () => {
        it('should throw if private review not found', async () => {
            const call = async () =>
                await linkClientToPrivateReviewUseCase.execute({
                    privateReviewId: newDbId().toString(),
                    email: '<EMAIL>',
                });

            await expect(call).rejects.toThrow(expect.objectContaining({ malouErrorCode: MalouErrorCode.PRIVATE_REVIEW_NOT_FOUND }));
        });

        it('should link existing client to private review using clientId', async () => {
            const testCase = new TestCaseBuilderV2<'clients' | 'privateReviews'>({
                seeds: {
                    clients: {
                        data() {
                            return [getDefaultClient().build()];
                        },
                    },
                    privateReviews: {
                        data(deps) {
                            const client = deps.clients()[0];
                            return [getDefaultPrivateReview().restaurantId(client.restaurantId).build()];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const client = dependencies.clients[0];
                    const privateReview = dependencies.privateReviews[0];
                    return {
                        ...cleanPrivateReviewFromIds(privateReview),
                        clientId: client._id,
                    };
                },
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const client = seededObjects.clients[0];
            const privateReview = seededObjects.privateReviews[0];

            const result = await linkClientToPrivateReviewUseCase.execute({
                privateReviewId: privateReview._id.toString(),
                clientId: client._id.toString(),
            });

            const expectedResult = testCase.getExpectedResult();
            expect(omitUpdatedAt(result)).toEqual(omitUpdatedAt(expectedResult));

            const updatedClient = await clientsRepository.findOne({ filter: { _id: client._id } });
            expect(updatedClient?.visitCount).toBe((client.visitCount ?? 0) + 1);
            expect(updatedClient?.accepts).toEqual([ContactMode.EMAIL, ContactMode.SMS]);
            expect(updatedClient?.reviewsLeft?.length).toBe(1);
            expect(updatedClient?.reviewsLeft?.[0]).toMatchObject({
                platformKey: PRIVATE_NEGATIVE_REVIEW_PLATFORM_KEY,
                hasLeftReview: true,
            });
        });

        it('should link existing client to private review using email', async () => {
            const email = '<EMAIL>';
            const testCase = new TestCaseBuilderV2<'clients' | 'privateReviews'>({
                seeds: {
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    .email(email)
                                    .reviewsLeft([
                                        {
                                            platformKey: PRIVATE_NEGATIVE_REVIEW_PLATFORM_KEY,
                                            hasLeftReview: true,
                                        },
                                        {
                                            platformKey: PlatformKey.GMB,
                                            hasLeftReview: false,
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                    privateReviews: {
                        data(deps) {
                            const client = deps.clients()[0];
                            return [getDefaultPrivateReview().restaurantId(client.restaurantId).build()];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const client = dependencies.clients[0];
                    const privateReview = dependencies.privateReviews[0];
                    return { ...cleanPrivateReviewFromIds(privateReview), clientId: client._id };
                },
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const client = seededObjects.clients[0];
            const privateReview = seededObjects.privateReviews[0];

            const result = await linkClientToPrivateReviewUseCase.execute({
                privateReviewId: privateReview._id.toString(),
                email,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(omitUpdatedAt(result)).toEqual(omitUpdatedAt(expectedResult));

            const updatedClient = await clientsRepository.findOne({ filter: { _id: client._id } });
            expect(updatedClient?.visitCount).toBe((client.visitCount ?? 0) + 1);
            expect(updatedClient?.accepts).toEqual([ContactMode.EMAIL, ContactMode.SMS]);
            expect(updatedClient?.reviewsLeft?.length).toBe(3);
            expect(
                updatedClient?.reviewsLeft?.find(({ platformKey }) => platformKey === PRIVATE_NEGATIVE_REVIEW_PLATFORM_KEY)
            ).toMatchObject({
                platformKey: PRIVATE_NEGATIVE_REVIEW_PLATFORM_KEY,
                hasLeftReview: true,
            });
        });

        it('should create a new client and link it to private review when no client matches email', async () => {
            const restaurantId = newDbId();
            const testCase = new TestCaseBuilderV2<'clients' | 'privateReviews'>({
                seeds: {
                    clients: {
                        data() {
                            return [];
                        },
                    },
                    privateReviews: {
                        data() {
                            return [getDefaultPrivateReview().restaurantId(restaurantId).build()];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const privateReview = dependencies.privateReviews[0];
                    return {
                        ...cleanPrivateReviewFromIds(privateReview),
                        clientId: expect.anything(),
                    };
                },
            });
            await testCase.build();

            const privateReview = testCase.getSeededObjects().privateReviews[0];
            const email = '<EMAIL>';

            const result = await linkClientToPrivateReviewUseCase.execute({
                privateReviewId: privateReview._id.toString(),
                email,
            });

            const expectedResult = testCase.getExpectedResult();
            expect(omitUpdatedAt(result)).toEqual(omitUpdatedAt(expectedResult));

            const createdClient = await clientsRepository.findOne({ filter: { _id: result.clientId } });
            expect(createdClient).toBeDefined();
            expect(createdClient?.email).toBe(email);
            expect(createdClient?.visitCount).toBe(1);
            expect(createdClient?.lastName).toBe('Client totem');
            expect(createdClient?.source).toBe(ClientSource.TOTEMS);
            expect(createdClient?.accepts).toEqual([ContactMode.EMAIL, ContactMode.SMS]);
            expect(createdClient?.reviewsLeft?.length).toBe(1);
            expect(createdClient?.reviewsLeft?.[0]).toMatchObject({
                platformKey: PRIVATE_NEGATIVE_REVIEW_PLATFORM_KEY,
                hasLeftReview: true,
            });
        });
    });
});

const cleanPrivateReviewFromIds = (privateReview: any) => {
    return {
        ...privateReview,
        comments: privateReview.comments.map((comment) => ({
            ...omit(comment, 'id'),
            author: omit(comment.author, 'id'),
            content: omit(comment.content, 'id'),
        })),
    };
};

const omitUpdatedAt = (privateReview: any) => omit(privateReview, 'updatedAt');
