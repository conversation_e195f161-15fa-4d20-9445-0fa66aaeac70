import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { FixSortDateTask } from ':tasks/stories/fix-sort-date/fix-sort-date';

describe('FixSortDateTask', () => {
    beforeAll(() => {
        registerRepositories(['PostsRepository']);
    });

    describe('should update sortDate for stories', () => {
        it('should set sortDate to socialCreatedAt when socialCreatedAt is available', async () => {
            const socialCreatedAt = DateTime.now().minus({ days: 5 }).toJSDate();
            const plannedPublicationDate = DateTime.now().plus({ days: 2 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .socialCreatedAt(socialCreatedAt)
                                    .plannedPublicationDate(plannedPublicationDate)
                                    .sortDate(new Date('2020-01-01')) // Old incorrect sortDate
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const fixSortDateTask = container.resolve(FixSortDateTask);

            // Verify the story exists with old sortDate
            const storiesBeforeUpdate = await storiesRepository.find({
                filter: { isStory: true },
            });
            expect(storiesBeforeUpdate.length).toBe(1);
            expect(storiesBeforeUpdate[0].sortDate).toEqual(new Date('2020-01-01'));

            // Execute the task
            await fixSortDateTask.execute();

            // Verify the sortDate was updated to socialCreatedAt
            const storiesAfterUpdate = await storiesRepository.find({
                filter: { isStory: true },
            });
            expect(storiesAfterUpdate.length).toBe(1);
            expect(storiesAfterUpdate[0].sortDate).toEqual(socialCreatedAt);
        });

        it('should set sortDate to plannedPublicationDate when socialCreatedAt is null', async () => {
            const plannedPublicationDate = DateTime.now().plus({ days: 3 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .socialCreatedAt(undefined)
                                    .plannedPublicationDate(plannedPublicationDate)
                                    .sortDate(new Date('2020-01-01')) // Old incorrect sortDate
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const fixSortDateTask = container.resolve(FixSortDateTask);

            // Verify the story exists with old sortDate
            const storiesBeforeUpdate = await storiesRepository.find({
                filter: { isStory: true },
            });
            expect(storiesBeforeUpdate.length).toBe(1);
            expect(storiesBeforeUpdate[0].sortDate).toEqual(new Date('2020-01-01'));

            // Execute the task
            await fixSortDateTask.execute();

            // Verify the sortDate was updated to plannedPublicationDate
            const storiesAfterUpdate = await storiesRepository.find({
                filter: { isStory: true },
            });
            expect(storiesAfterUpdate.length).toBe(1);
            expect(storiesAfterUpdate[0].sortDate).toEqual(plannedPublicationDate);
        });

        it('should set sortDate to current date when both socialCreatedAt and plannedPublicationDate are null', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .socialCreatedAt(undefined)
                                    .plannedPublicationDate(null)
                                    .sortDate(new Date('2020-01-01')) // Old incorrect sortDate
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const fixSortDateTask = container.resolve(FixSortDateTask);

            const beforeExecutionTime = new Date();

            // Execute the task
            await fixSortDateTask.execute();

            const afterExecutionTime = new Date();

            // Verify the sortDate was updated to a current date
            const storiesAfterUpdate = await storiesRepository.find({
                filter: { isStory: true },
            });
            expect(storiesAfterUpdate.length).toBe(1);
            expect(storiesAfterUpdate[0].sortDate?.getTime()).toBeGreaterThanOrEqual(beforeExecutionTime.getTime());
            expect(storiesAfterUpdate[0].sortDate?.getTime()).toBeLessThanOrEqual(afterExecutionTime.getTime());
        });

        it('should update multiple stories with different scenarios', async () => {
            const socialCreatedAt1 = DateTime.now().minus({ days: 10 }).toJSDate();
            const plannedPublicationDate2 = DateTime.now().plus({ days: 5 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                // Story 1: has socialCreatedAt
                                getDefaultPost()
                                    .isStory(true)
                                    .socialCreatedAt(socialCreatedAt1)
                                    .plannedPublicationDate(null)
                                    .sortDate(new Date('2020-01-01'))
                                    .build(),
                                // Story 2: has plannedPublicationDate but no socialCreatedAt
                                getDefaultPost()
                                    .isStory(true)
                                    .socialCreatedAt(undefined)
                                    .plannedPublicationDate(plannedPublicationDate2)
                                    .sortDate(new Date('2020-01-01'))
                                    .build(),
                                // Story 3: has neither
                                getDefaultPost()
                                    .isStory(true)
                                    .socialCreatedAt(undefined)
                                    .plannedPublicationDate(null)
                                    .sortDate(new Date('2020-01-01'))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const fixSortDateTask = container.resolve(FixSortDateTask);

            const beforeExecutionTime = new Date();

            // Execute the task
            await fixSortDateTask.execute();

            const afterExecutionTime = new Date();

            // Verify all stories were updated correctly
            const storiesAfterUpdate = await storiesRepository.find({
                filter: { isStory: true },
                options: {
                    lean: true,
                },
            });
            expect(storiesAfterUpdate.length).toBe(3);

            // Story 1: sortDate should be socialCreatedAt1
            expect(storiesAfterUpdate[0].sortDate).toEqual(socialCreatedAt1);

            // Story 2: sortDate should be plannedPublicationDate2
            expect(storiesAfterUpdate[1].sortDate).toEqual(plannedPublicationDate2);

            // Story 3: sortDate should be current date
            expect(storiesAfterUpdate[2].sortDate?.getTime()).toBeGreaterThanOrEqual(beforeExecutionTime.getTime());
            expect(storiesAfterUpdate[2].sortDate?.getTime()).toBeLessThanOrEqual(afterExecutionTime.getTime());
        });
    });

    describe('should only update stories', () => {
        it('should not update non-story posts', async () => {
            const socialCreatedAt = DateTime.now().minus({ days: 5 }).toJSDate();
            const originalSortDate = new Date('2020-01-01');

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(false) // Not a story
                                    .socialCreatedAt(socialCreatedAt)
                                    .sortDate(originalSortDate)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const fixSortDateTask = container.resolve(FixSortDateTask);

            // Execute the task
            await fixSortDateTask.execute();

            // Verify the non-story post was NOT updated
            const postsAfterUpdate = await storiesRepository.find({
                filter: { isStory: false },
            });
            expect(postsAfterUpdate.length).toBe(1);
            expect(postsAfterUpdate[0].sortDate).toEqual(originalSortDate);
        });

        it('should handle mixed stories and non-stories correctly', async () => {
            const socialCreatedAt = DateTime.now().minus({ days: 5 }).toJSDate();
            const originalSortDate = new Date('2020-01-01');

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                // Story - should be updated
                                getDefaultPost().isStory(true).socialCreatedAt(socialCreatedAt).sortDate(originalSortDate).build(),
                                // Non-story - should NOT be updated
                                getDefaultPost().isStory(false).socialCreatedAt(socialCreatedAt).sortDate(originalSortDate).build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const fixSortDateTask = container.resolve(FixSortDateTask);

            // Execute the task
            await fixSortDateTask.execute();

            // Verify the story was updated
            const storiesAfterUpdate = await storiesRepository.find({
                filter: { isStory: true },
            });
            expect(storiesAfterUpdate.length).toBe(1);
            expect(storiesAfterUpdate[0].sortDate).toEqual(socialCreatedAt);

            // Verify the non-story was NOT updated
            const postsAfterUpdate = await storiesRepository.find({
                filter: { isStory: false },
            });
            expect(postsAfterUpdate.length).toBe(1);
            expect(postsAfterUpdate[0].sortDate).toEqual(originalSortDate);
        });
    });

    describe('edge cases', () => {
        it('should handle empty database gracefully', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const fixSortDateTask = container.resolve(FixSortDateTask);

            // Verify no posts exist
            const postsBeforeUpdate = await storiesRepository.find({ filter: {} });
            expect(postsBeforeUpdate.length).toBe(0);

            // Execute the task (should not throw error)
            await expect(fixSortDateTask.execute()).resolves.not.toThrow();

            // Verify still no posts exist
            const postsAfterUpdate = await storiesRepository.find({ filter: {} });
            expect(postsAfterUpdate.length).toBe(0);
        });

        it('should handle stories that already have correct sortDate', async () => {
            const socialCreatedAt = DateTime.now().minus({ days: 5 }).toJSDate();
            const correctSortDate = socialCreatedAt; // Already correct

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [getDefaultPost().isStory(true).socialCreatedAt(socialCreatedAt).sortDate(correctSortDate).build()];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const fixSortDateTask = container.resolve(FixSortDateTask);

            // Execute the task
            await fixSortDateTask.execute();

            // Verify the sortDate remains the same (still correct)
            const storiesAfterUpdate = await storiesRepository.find({
                filter: { isStory: true },
            });
            expect(storiesAfterUpdate.length).toBe(1);
            expect(storiesAfterUpdate[0].sortDate).toEqual(socialCreatedAt);
        });

        it('should prioritize socialCreatedAt over plannedPublicationDate when both are available', async () => {
            const socialCreatedAt = DateTime.now().minus({ days: 5 }).toJSDate();
            const plannedPublicationDate = DateTime.now().plus({ days: 2 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .socialCreatedAt(socialCreatedAt)
                                    .plannedPublicationDate(plannedPublicationDate)
                                    .sortDate(new Date('2020-01-01'))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const fixSortDateTask = container.resolve(FixSortDateTask);

            // Execute the task
            await fixSortDateTask.execute();

            // Verify the sortDate was set to socialCreatedAt (not plannedPublicationDate)
            const storiesAfterUpdate = await storiesRepository.find({
                filter: { isStory: true },
            });
            expect(storiesAfterUpdate.length).toBe(1);
            expect(storiesAfterUpdate[0].sortDate).toEqual(socialCreatedAt);
            expect(storiesAfterUpdate[0].sortDate).not.toEqual(plannedPublicationDate);
        });
    });
});
