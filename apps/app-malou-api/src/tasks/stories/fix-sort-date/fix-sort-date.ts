import { autoInjectable } from 'tsyringe';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@autoInjectable()
export class FixSortDateTask {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute() {
        const cursor = this._storiesRepository.model
            .find(
                {
                    isStory: true,
                },
                { _id: 1, socialCreatedAt: 1, plannedPublicationDate: 1 },
                { lean: true }
            )
            .cursor();

        let count = 0;

        await cursor.eachAsync(async (story) => {
            await this._storiesRepository.updateOne({
                filter: { _id: story._id },
                update: { $set: { sortDate: story.socialCreatedAt ?? story.plannedPublicationDate ?? new Date() } },
            });
            count += 1;
            console.log('Updated', count, 'stories');
        });
    }
}
