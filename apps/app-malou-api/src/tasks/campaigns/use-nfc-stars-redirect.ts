import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { NfcStar, RedirectStar } from '@malou-io/package-utils';

import CampaignsRepository from ':modules/campaigns/campaigns.repository';
import ':plugins/db';

@singleton()
class UseNfcStarsForRedirectTask {
    constructor(private readonly _campaignsRepository: CampaignsRepository) {}

    async execute() {
        await this._campaignsRepository.updateMany({
            filter: {
                redirectFrom: RedirectStar.FOUR_STARS,
            },
            update: {
                $set: {
                    privateReviewRatings: [NfcStar.ONE, NfcStar.TWO, NfcStar.THREE].map((star) => parseInt(star.toString(), 10)),
                },
            },
        });

        await this._campaignsRepository.updateMany({
            filter: {
                redirectFrom: RedirectStar.FIVE_STARS,
            },
            update: {
                $set: {
                    privateReviewRatings: [NfcStar.ONE, NfcStar.TWO, NfcStar.THREE, NfcStar.FOUR].map((star) =>
                        parseInt(star.toString(), 10)
                    ),
                },
            },
        });
    }
}

const task = container.resolve(UseNfcStarsForRedirectTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
