import 'reflect-metadata';

// Required for tsyringe
import ':env';

import { autoInjectable } from 'tsyringe';

import { toDbIds } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus, PostSource, PostType } from '@malou-io/package-utils';

import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import ':plugins/db';

@autoInjectable()
export class RemoveTitleFromReelsAndInstaFbPostsTask {
    constructor(private readonly _postsRepository: PostsRepository) {}
    async execute() {
        const cursor = this._postsRepository.model
            .find({
                source: PostSource.SOCIAL,
                published: PostPublicationStatus.PUBLISHED,
                title: { $exists: true, $nin: [null, ''] },
                $or: [{ postType: PostType.REEL }, { key: { $in: [PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK] } }],
            })
            .cursor();

        let count = 0;

        await cursor.eachAsync(
            async (posts) => {
                const postIds = posts.map((p) => p._id.toString());
                await this._postsRepository.updateMany({
                    filter: { _id: { $in: toDbIds(postIds) } },
                    update: { title: '' },
                });
                count += posts.length;
                console.log(`Updated ${count} posts`);
            },
            { batchSize: 100 }
        );
    }
}
