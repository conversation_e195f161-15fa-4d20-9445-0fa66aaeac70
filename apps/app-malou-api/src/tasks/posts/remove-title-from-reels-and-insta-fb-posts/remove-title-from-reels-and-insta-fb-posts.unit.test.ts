import { container } from 'tsyringe';

import { PlatformKey, PostPublicationStatus, PostSource, PostType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { RemoveTitleFromReelsAndInstaFbPostsTask } from ':tasks/posts/remove-title-from-reels-and-insta-fb-posts/remove-title-from-reels-and-insta-fb-posts';

describe('RemoveTitleFromReelsAndInstaFbPostsTask', () => {
    beforeAll(() => {
        registerRepositories(['PostsRepository']);
    });

    describe('should remove titles from posts that meet criteria', () => {
        it('should remove title from published social reel posts', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .postType(PostType.REEL)
                                    .title('This title should be removed')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const postsRepository = container.resolve(PostsRepository);
            const removeTitleTask = new RemoveTitleFromReelsAndInstaFbPostsTask(postsRepository);

            // Verify the post has a title before task execution
            const postsBeforeUpdate = await postsRepository.find({
                filter: { source: PostSource.SOCIAL, published: PostPublicationStatus.PUBLISHED },
            });
            expect(postsBeforeUpdate.length).toBe(1);
            expect(postsBeforeUpdate[0].title).toBe('This title should be removed');

            // Execute the task
            await removeTitleTask.execute();

            // Verify the title was removed
            const postsAfterUpdate = await postsRepository.find({
                filter: { source: PostSource.SOCIAL, published: PostPublicationStatus.PUBLISHED },
            });
            expect(postsAfterUpdate.length).toBe(1);
            expect(postsAfterUpdate[0].title).toBe('');
        });

        it('should remove title from published social Instagram posts', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .key(PlatformKey.INSTAGRAM)
                                    .postType(PostType.IMAGE)
                                    .title('Instagram title to remove')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const postsRepository = container.resolve(PostsRepository);
            const removeTitleTask = new RemoveTitleFromReelsAndInstaFbPostsTask(postsRepository);

            // Verify the post has a title before task execution
            const postsBeforeUpdate = await postsRepository.find({
                filter: { key: PlatformKey.INSTAGRAM },
            });
            expect(postsBeforeUpdate.length).toBe(1);
            expect(postsBeforeUpdate[0].title).toBe('Instagram title to remove');

            // Execute the task
            await removeTitleTask.execute();

            // Verify the title was removed
            const postsAfterUpdate = await postsRepository.find({
                filter: { key: PlatformKey.INSTAGRAM },
            });
            expect(postsAfterUpdate.length).toBe(1);
            expect(postsAfterUpdate[0].title).toBe('');
        });

        it('should remove title from published social Facebook posts', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .key(PlatformKey.FACEBOOK)
                                    .postType(PostType.IMAGE)
                                    .title('Facebook title to remove')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const postsRepository = container.resolve(PostsRepository);
            const removeTitleTask = new RemoveTitleFromReelsAndInstaFbPostsTask(postsRepository);

            // Verify the post has a title before task execution
            const postsBeforeUpdate = await postsRepository.find({
                filter: { key: PlatformKey.FACEBOOK },
            });
            expect(postsBeforeUpdate.length).toBe(1);
            expect(postsBeforeUpdate[0].title).toBe('Facebook title to remove');

            // Execute the task
            await removeTitleTask.execute();

            // Verify the title was removed
            const postsAfterUpdate = await postsRepository.find({
                filter: { key: PlatformKey.FACEBOOK },
            });
            expect(postsAfterUpdate.length).toBe(1);
            expect(postsAfterUpdate[0].title).toBe('');
        });

        it('should remove titles from multiple posts that meet criteria', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .postType(PostType.REEL)
                                    .title('Reel title')
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .key(PlatformKey.INSTAGRAM)
                                    .postType(PostType.IMAGE)
                                    .title('Instagram title')
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .key(PlatformKey.FACEBOOK)
                                    .postType(PostType.VIDEO)
                                    .title('Facebook title')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const postsRepository = container.resolve(PostsRepository);
            const removeTitleTask = new RemoveTitleFromReelsAndInstaFbPostsTask(postsRepository);

            // Verify all posts have titles before task execution
            const postsBeforeUpdate = await postsRepository.find({
                filter: { source: PostSource.SOCIAL, published: PostPublicationStatus.PUBLISHED },
            });
            expect(postsBeforeUpdate.length).toBe(3);
            expect(postsBeforeUpdate.every((post) => post.title && post.title !== '')).toBe(true);

            // Execute the task
            await removeTitleTask.execute();

            // Verify all titles were removed
            const postsAfterUpdate = await postsRepository.find({
                filter: { source: PostSource.SOCIAL, published: PostPublicationStatus.PUBLISHED },
            });
            expect(postsAfterUpdate.length).toBe(3);
            expect(postsAfterUpdate.every((post) => post.title === '')).toBe(true);
        });
    });

    describe('should not remove titles from posts that do not meet criteria', () => {
        it('should not remove title from SEO posts', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SEO)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .postType(PostType.REEL)
                                    .title('SEO title should remain')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const postsRepository = container.resolve(PostsRepository);
            const removeTitleTask = new RemoveTitleFromReelsAndInstaFbPostsTask(postsRepository);

            // Verify the post has a title before task execution
            const postsBeforeUpdate = await postsRepository.find({
                filter: { source: PostSource.SEO },
            });
            expect(postsBeforeUpdate.length).toBe(1);
            expect(postsBeforeUpdate[0].title).toBe('SEO title should remain');

            // Execute the task
            await removeTitleTask.execute();

            // Verify the title was NOT removed (SEO source)
            const postsAfterUpdate = await postsRepository.find({
                filter: { source: PostSource.SEO },
            });
            expect(postsAfterUpdate.length).toBe(1);
            expect(postsAfterUpdate[0].title).toBe('SEO title should remain');
        });

        it('should not remove title from draft posts', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.DRAFT)
                                    .postType(PostType.REEL)
                                    .title('Draft title should remain')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const postsRepository = container.resolve(PostsRepository);
            const removeTitleTask = new RemoveTitleFromReelsAndInstaFbPostsTask(postsRepository);

            // Verify the post has a title before task execution
            const postsBeforeUpdate = await postsRepository.find({
                filter: { published: PostPublicationStatus.DRAFT },
            });
            expect(postsBeforeUpdate.length).toBe(1);
            expect(postsBeforeUpdate[0].title).toBe('Draft title should remain');

            // Execute the task
            await removeTitleTask.execute();

            // Verify the title was NOT removed (draft status)
            const postsAfterUpdate = await postsRepository.find({
                filter: { published: PostPublicationStatus.DRAFT },
            });
            expect(postsAfterUpdate.length).toBe(1);
            expect(postsAfterUpdate[0].title).toBe('Draft title should remain');
        });

        it('should not remove title from posts with null or empty title', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .postType(PostType.REEL)
                                    .title(undefined)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .key(PlatformKey.INSTAGRAM)
                                    .title('')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const postsRepository = container.resolve(PostsRepository);
            const removeTitleTask = new RemoveTitleFromReelsAndInstaFbPostsTask(postsRepository);

            // Verify posts exist before task execution
            const postsBeforeUpdate = await postsRepository.find({
                filter: { source: PostSource.SOCIAL, published: PostPublicationStatus.PUBLISHED },
            });
            expect(postsBeforeUpdate.length).toBe(2);

            // Execute the task
            await removeTitleTask.execute();

            // Verify posts still exist (should not be affected)
            const postsAfterUpdate = await postsRepository.find({
                filter: { source: PostSource.SOCIAL, published: PostPublicationStatus.PUBLISHED },
            });
            expect(postsAfterUpdate.length).toBe(2);
        });

        it('should not remove title from non-reel, non-Instagram, non-Facebook posts', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SEO)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .key(PlatformKey.GMB)
                                    .postType(PostType.IMAGE)
                                    .title('GMB title should remain')
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .key(PlatformKey.TIKTOK)
                                    .postType(PostType.VIDEO)
                                    .title('TikTok title should remain')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const postsRepository = container.resolve(PostsRepository);
            const removeTitleTask = new RemoveTitleFromReelsAndInstaFbPostsTask(postsRepository);

            // Verify posts have titles before task execution
            const postsBeforeUpdate = await postsRepository.find({ filter: {} });
            expect(postsBeforeUpdate.length).toBe(2);
            const gmbPost = postsBeforeUpdate.find((p) => p.key === PlatformKey.GMB);
            const tiktokPost = postsBeforeUpdate.find((p) => p.key === PlatformKey.TIKTOK);
            expect(gmbPost?.title).toBe('GMB title should remain');
            expect(tiktokPost?.title).toBe('TikTok title should remain');

            // Execute the task
            await removeTitleTask.execute();

            // Verify titles were NOT removed (different platforms)
            const postsAfterUpdate = await postsRepository.find({
                filter: {},
            });
            expect(postsAfterUpdate.length).toBe(2);
            const gmbPostAfterUpdate = postsAfterUpdate.find((p) => p.key === PlatformKey.GMB);
            const tiktokPostAfterUpdate = postsAfterUpdate.find((p) => p.key === PlatformKey.TIKTOK);
            expect(gmbPostAfterUpdate?.title).toBe('GMB title should remain');
            expect(tiktokPostAfterUpdate?.title).toBe('TikTok title should remain');
        });
    });

    describe('edge cases and mixed scenarios', () => {
        it('should handle empty database gracefully', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            const postsRepository = container.resolve(PostsRepository);
            const removeTitleTask = new RemoveTitleFromReelsAndInstaFbPostsTask(postsRepository);

            // Execute the task on empty database
            await expect(removeTitleTask.execute()).resolves.not.toThrow();

            // Verify database is still empty
            const postsAfterUpdate = await postsRepository.find({ filter: {} });
            expect(postsAfterUpdate.length).toBe(0);
        });

        it('should only update posts that meet criteria in mixed scenario', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                // Should be updated: REEL
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .postType(PostType.REEL)
                                    .title('Reel title to remove')
                                    .build(),
                                // Should be updated: Instagram
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .postType(PostType.IMAGE)
                                    .key(PlatformKey.INSTAGRAM)
                                    .title('Instagram title to remove')
                                    .build(),
                                // Should NOT be updated: Draft
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.DRAFT)
                                    .postType(PostType.REEL)
                                    .title('Draft title should remain')
                                    .build(),
                                // Should NOT be updated: SEO source
                                getDefaultPost()
                                    .source(PostSource.SEO)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .key(PlatformKey.GMB)
                                    .title('SEO title should remain')
                                    .build(),
                                // Should NOT be updated: TikTok platform
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .key(PlatformKey.TIKTOK)
                                    .title('TikTok title should remain')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const postsBeforeUpdate = testCase.getSeededObjects().posts;

            const postsRepository = container.resolve(PostsRepository);
            const removeTitleTask = new RemoveTitleFromReelsAndInstaFbPostsTask(postsRepository);

            // Execute the task
            await removeTitleTask.execute();

            // Verify only the correct posts were updated
            const allPosts = await postsRepository.find({ filter: {} });
            expect(allPosts.length).toBe(5);

            // Posts that should have titles removed
            const reelPost = allPosts.find((p) => p._id.toString() === postsBeforeUpdate[0]._id.toString());
            const instagramPost = allPosts.find((p) => p._id.toString() === postsBeforeUpdate[1]._id.toString());
            expect(reelPost?.title).toBe('');
            expect(instagramPost?.title).toBe('');

            // Posts that should keep their titles
            const draftPost = allPosts.find((p) => p.published === PostPublicationStatus.DRAFT);
            const seoPost = allPosts.find((p) => p.source === PostSource.SEO);
            const tiktokPost = allPosts.find((p) => p.key === PlatformKey.TIKTOK);
            expect(draftPost?.title).toBe('Draft title should remain');
            expect(seoPost?.title).toBe('SEO title should remain');
            expect(tiktokPost?.title).toBe('TikTok title should remain');
        });
    });
});
