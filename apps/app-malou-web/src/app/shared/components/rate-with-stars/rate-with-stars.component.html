<div class="flex h-full w-full gap-x-2" (mouseleave)="starHoveredIndex.set(null)">
    @for (_ of 5 | createArray; track $index; let index = $index) {
        <div class="flex-1" (mouseenter)="starHoveredIndex.set(index + 1)">
            <mat-icon
                class="malou-color-state-warn !h-full !w-full"
                [ngClass]="{ 'cursor-pointer': !readOnly }"
                [svgIcon]="(fullStars() | includes: index + 1) ? SvgIcon.STAR : SvgIcon.STAR_BORDER"
                (click)="onStarClicked(index)"></mat-icon>
        </div>
    }
</div>
