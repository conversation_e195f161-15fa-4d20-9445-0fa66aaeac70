import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input, output, signal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

import { STARS_RATING } from ':core/constants';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { CreateArrayPipe } from ':shared/pipes/create-array.pipe';
import { IncludesPipe } from ':shared/pipes/includes.pipe';

@Component({
    selector: 'app-rate-with-stars',
    templateUrl: './rate-with-stars.component.html',
    styleUrls: ['./rate-with-stars.component.scss'],
    imports: [NgClass, MatIconModule, CreateArrayPipe, IncludesPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RateWithStarsComponent {
    readonly defaultStarRating = input<number>();
    readonly readOnly = input<boolean>(false);
    readonly starClicked = output<number>();

    readonly SvgIcon = SvgIcon;

    readonly starHoveredIndex = signal<number | null>(null);

    readonly fullStars = computed(() => {
        const allRatings = STARS_RATING;
        const starHoveredIndex = this.starHoveredIndex();
        const defaultStarRating = this.defaultStarRating();

        if (starHoveredIndex !== null) {
            return allRatings.filter((rating) => rating <= starHoveredIndex);
        }

        if (defaultStarRating) {
            return allRatings.filter((rating) => rating <= defaultStarRating);
        }

        return [];
    });

    onStarClicked(index: number): void {
        if (this.readOnly()) {
            return;
        }
        this.starClicked.emit(index + 1);
    }
}
