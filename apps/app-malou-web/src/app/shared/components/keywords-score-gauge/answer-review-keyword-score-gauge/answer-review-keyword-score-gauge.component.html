<div class="flex flex-col" data-testid="keywords-score-brick-container">
    <div class="malou-color-background-dark mb-0.5 mt-2 flex w-full flex-col rounded-t-[10px] p-[22px] !pt-[10px]">
        <div class="mb-3 flex items-center justify-between">
            <div class="malou-text-12--bold text-malou-color-text-1">{{ 'keywords_score.keywords' | translate }}</div>
            <div class="flex items-center gap-2">
                <app-simple-keyword-score-gauge
                    [shouldDisplayIndicationsTooltip]="true"
                    [withLargeTitle]="true"></app-simple-keyword-score-gauge>
                <app-select
                    [testId]="'keywords-score-gauge-bricks-lang-input'"
                    [formControl]="keywordsScoreGaugeContext.brickLangControl"
                    [values]="keywordsScoreGaugeContext.langOptions()"
                    [displayWith]="keywordsScoreGaugeContext.brickLangDisplayFn"
                    [idPrefix]="'tracking_keywords_score_bricks_lang'"
                    [getIdSuffixFn]="keywordsScoreGaugeContext.getIdSuffixFn">
                </app-select>
            </div>
        </div>
        @if (bricksToDisplay()) {
            <div class="flex flex-wrap gap-x-2">
                @for (brick of bricksToDisplay(); track trackByTextFn($index, brick)) {
                    <div class="mb-2 flex" data-testid="keywords-score-brick" (click)="keywordsScoreGaugeContext.addBrick(brick)" #isFound>
                        <app-single-clickable-brick
                            [brick]="brick"
                            [asChip]="true"
                            [isBrickChecked]="keywordsScoreGaugeContext.isBrickChecked()"
                            [brickTranslated]="keywordsScoreGaugeContext.brickTranslated()"
                            [areDisabledBricks]="keywordsScoreGaugeContext.areDisabledBricks()"
                            [shouldDisplayKeywordsCategory]="false"></app-single-clickable-brick>
                    </div>
                }
            </div>
        }
    </div>
    <div class="malou-color-background-dark !m-0 w-full !flex-auto rounded-b-[10px] p-[22px]">
        <div class="mb-2 flex items-start gap-3">
            <div class="malou-text-12--bold mb-4 mt-1 text-malou-color-text-1">{{ 'keywords_score.reply_tips' | translate }}</div>
        </div>
        <app-keywords-score-tips
            class="flex gap-2"
            [indicationsList]="keywordsScoreGaugeContext.indicationsList()"></app-keywords-score-tips>
    </div>
</div>
