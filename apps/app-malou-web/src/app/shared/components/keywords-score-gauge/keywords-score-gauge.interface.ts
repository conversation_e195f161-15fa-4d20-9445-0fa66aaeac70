import { KeywordsScoreFulfilledValueDto } from '@malou-io/package-dto';
import { KeywordConditionCriteria, KeywordScoreTextType, PartialRecord } from '@malou-io/package-utils';

export enum FulfilledIndication {
    YES = 'YES',
    NO = 'NO',
    NOT_APPLICABLE = 'NOT_APPLICABLE',
}

export interface Indication {
    fulfilled: FulfilledIndication;
    currentValue: string;
    textType: KeywordConditionCriteria;
    shouldDisplayCurrentValue: boolean;
    shouldDisplaySubText: boolean;
    optimalValue?: number;
    minimumValue?: number;
    maximumValue?: number;
}

export enum KeywordsScoreGaugeType {
    SIMPLE = 'SIMPLE',
    SIMPLE_AS_COLUMN = 'SIMPLE_AS_COLUMN', // used for ReviewPreview
    SIMPLE_WITH_KEYWORDS_TOOLTIP = 'SIMPLE_WITH_KEYWORDS_TOOLTIP', // used for SeoDuplicationModal
    SIMPLE_WITH_INDICATIONS_TOOLTIP = 'SIMPLE_WITH_INDICATIONS_TOOLTIP', // used for AiReviewSettings
    DETAILED = 'DETAILED', // used for DescriptionModal, EditCreateTemplate
    DETAILED_DARK = 'DETAILED_DARK', // used for NewPostModal
    SEO_DUPLICATION = 'SEO_DUPLICATION', // used for SeoDuplicationOriginalPost
    ANSWER_REVIEW = 'ANSWER_REVIEW', // used for AnswerReview
}

export interface KeywordScoreDetail {
    fulfilled: boolean;
    fulfilledValue: KeywordsScoreFulfilledValueDto;
    value: number;
    currentValue: string;
}

type KeywordScoreRecord = {
    [textType in KeywordScoreTextType]: PartialRecord<KeywordConditionCriteria, KeywordScoreDetail>;
};

interface KeywordConditionCriteriaByTextType extends KeywordScoreRecord {
    [KeywordScoreTextType.POST]: {
        [KeywordConditionCriteria.BRICKS_NUMBER]: KeywordScoreDetail;
        [KeywordConditionCriteria.BRICKS_VARIETY]: KeywordScoreDetail;
        [KeywordConditionCriteria.RESTAURANT_NAME]: KeywordScoreDetail;
    };
    [KeywordScoreTextType.SHORT_DESCRIPTION]: {
        [KeywordConditionCriteria.BRICKS_NUMBER]: KeywordScoreDetail;
        [KeywordConditionCriteria.BRICKS_VARIETY]: KeywordScoreDetail;
        [KeywordConditionCriteria.RESTAURANT_NAME]: KeywordScoreDetail;
        [KeywordConditionCriteria.TEXT_LENGTH]: KeywordScoreDetail;
    };
    [KeywordScoreTextType.LONG_DESCRIPTION]: {
        [KeywordConditionCriteria.BRICKS_NUMBER]: KeywordScoreDetail;
        [KeywordConditionCriteria.BRICKS_VARIETY]: KeywordScoreDetail;
        [KeywordConditionCriteria.RESTAURANT_NAME]: KeywordScoreDetail;
        [KeywordConditionCriteria.TEXT_LENGTH]: KeywordScoreDetail;
    };
    [KeywordScoreTextType.HIGH_RATE_REVIEW]: {
        [KeywordConditionCriteria.BRICKS_NUMBER]: KeywordScoreDetail;
        [KeywordConditionCriteria.BRICKS_VARIETY]: KeywordScoreDetail;
        [KeywordConditionCriteria.RESTAURANT_NAME]: KeywordScoreDetail;
    };
    [KeywordScoreTextType.LOW_RATE_REVIEW]: {
        [KeywordConditionCriteria.BRICKS_NUMBER]: KeywordScoreDetail;
        [KeywordConditionCriteria.BRICKS_VARIETY]: KeywordScoreDetail;
        [KeywordConditionCriteria.RESTAURANT_NAME]: KeywordScoreDetail;
        [KeywordConditionCriteria.SORRY_WORDS]: KeywordScoreDetail;
    };
    [KeywordScoreTextType.HIGH_RATE_TEMPLATE]: {
        [KeywordConditionCriteria.BRICKS_NUMBER]: KeywordScoreDetail;
        [KeywordConditionCriteria.BRICKS_VARIETY]: KeywordScoreDetail;
    };
    [KeywordScoreTextType.LOW_RATE_TEMPLATE]: {
        [KeywordConditionCriteria.BRICKS_NUMBER]: KeywordScoreDetail;
        [KeywordConditionCriteria.SORRY_WORDS]: KeywordScoreDetail;
    };
}

export type ConditionsInterface<T extends KeywordScoreTextType> = KeywordConditionCriteriaByTextType[T];
