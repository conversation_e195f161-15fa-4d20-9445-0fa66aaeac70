import { AsyncPipe, NgClass, NgTemplateOutlet } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { catchError, combineLatest, EMPTY, map, Observable, of, shareReplay, switchMap, take } from 'rxjs';

import { NfcDto, NfcWithRestaurantDto, ScanDto } from '@malou-io/package-dto';
import {
    ApiResultV2,
    changePlatformUrlDomain,
    errorReplacer,
    getPlatformKeysNotRatedForTotems,
    NfcsPlatformKey,
    PlatformKey,
} from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { LangService } from ':core/services/lang.service';
import { NfcService } from ':core/services/nfc.service';
import { ScansService } from ':core/services/scans.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { RateWithStarsComponent } from ':shared/components/rate-with-stars/rate-with-stars.component';
import { Nfc } from ':shared/models';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-scan',
    templateUrl: './scan.component.html',
    styleUrls: ['./scan.component.scss'],
    imports: [
        NgClass,
        TranslateModule,
        MalouSpinnerComponent,
        AsyncPipe,
        MatIconModule,
        RateWithStarsComponent,
        NgTemplateOutlet,
        IllustrationPathResolverPipe,
        ImagePathResolverPipe,
    ],
})
export class ScanComponent implements OnInit {
    private readonly _route = inject(ActivatedRoute);
    private readonly _scansService = inject(ScansService);
    private readonly _nfcService = inject(NfcService);
    private readonly _langService = inject(LangService);
    private readonly _screenSizeService = inject(ScreenSizeService);
    private readonly _router = inject(Router);

    readonly Illustration = Illustration;
    readonly PlatformKey = PlatformKey;

    isError = false;
    boosterPackDeactivated = false;
    nfc$: Observable<NfcWithRestaurantDto>;
    starNumber: number;
    scan$: Observable<ScanDto>;
    data$: Observable<{ nfc: NfcWithRestaurantDto; scan: ScanDto }>;
    isRedirectionInProgress = false;

    readonly isPhoneScreen = toSignal(this._screenSizeService.isPhoneScreen$, { initialValue: this._screenSizeService.isPhoneScreen });

    ngOnInit(): void {
        const chipName = this._route.snapshot.queryParams.id;
        const restaurantId = this._route.snapshot.queryParams.restaurantId;
        if (!chipName && !restaurantId) {
            console.error('No chip name or restaurantId provided');
            this.isError = true;
            return;
        }
        this.nfc$ = chipName ? this.getTotem$(chipName) : this.getSticker$(restaurantId);
        this.scan$ = this.createScan$(this.nfc$);
        this.data$ = combineLatest({ nfc: this.nfc$, scan: this.scan$ });

        this.nfc$.subscribe((data) => {
            const nfc = Nfc.fromNfcWithRestaurantDto(data);
            if (nfc.isRedirectingToWheelOfFortune() || (nfc.platformKey && getPlatformKeysNotRatedForTotems().includes(nfc.platformKey))) {
                this.isRedirectionInProgress = true;
                this._patchScan().subscribe(() => {
                    if (nfc.redirectionLink) {
                        window.location.href = this._getUpdatedRedirectionLink({
                            redirectionLink: nfc.redirectionLink,
                            platformKey: nfc.platformKey,
                        });
                    }
                });
            }

            if (nfc.isRedirectingForAllStars()) {
                this.isRedirectionInProgress = true;
                this._patchScan().subscribe(() => {
                    if (nfc.redirectionLink) {
                        window.location.href = this._getUpdatedRedirectionLink({
                            redirectionLink: nfc.redirectionLink,
                            platformKey: nfc.platformKey,
                        });
                    }
                });
            }
        });
    }

    getTotem$(chipName: string): Observable<NfcWithRestaurantDto> {
        return this._nfcService.getNfcByChipname(chipName).pipe(
            map((apiResult: ApiResultV2<NfcWithRestaurantDto>) => apiResult.data),
            switchMap((nfc: NfcWithRestaurantDto) => {
                if (!nfc.active) {
                    console.error('NFC is not active');
                    this.isError = true;
                    return EMPTY;
                }
                if (!nfc.restaurant?.active) {
                    console.error('Restaurant is not active');
                    this.isError = true;
                    return EMPTY;
                }
                if (!nfc.restaurant?.boosterPack?.activated) {
                    console.error('Booster pack is not activated');
                    this.isError = true;
                    this.boosterPackDeactivated = true;
                    return EMPTY;
                }
                return of(nfc);
            }),
            catchError((e) => {
                console.error('Error when getting NFC', JSON.stringify(e, errorReplacer));
                this.isError = true;
                return EMPTY;
            }),
            shareReplay(1)
        );
    }

    getSticker$(restaurantId: string): Observable<NfcWithRestaurantDto> {
        return this._nfcService.getStickerByRestaurantId(restaurantId).pipe(
            map((apiResult: ApiResultV2<NfcWithRestaurantDto>) => apiResult.data),
            switchMap((nfc: NfcWithRestaurantDto) => {
                if (!nfc.active) {
                    console.error('NFC is not active');
                    this.isError = true;
                    return EMPTY;
                }
                if (!nfc.restaurant?.active) {
                    console.error('Restaurant is not active');
                    this.isError = true;
                    return EMPTY;
                }
                if (!nfc.restaurant?.boosterPack?.activated) {
                    console.error('Booster pack is not activated');
                    this.isError = true;
                    this.boosterPackDeactivated = true;
                    return EMPTY;
                }
                return of(nfc);
            }),
            catchError((e) => {
                console.error('Error when getting NFC', JSON.stringify(e, errorReplacer));
                this.isError = true;
                return EMPTY;
            }),
            shareReplay(1)
        );
    }

    createScan$(nfc$: Observable<NfcDto>): Observable<ScanDto> {
        return nfc$.pipe(
            switchMap((nfc) =>
                this._scansService.create({
                    nfcId: nfc.id,
                    scannedAt: new Date().toISOString(),
                    nfcSnapshot: nfc,
                })
            ),
            map((apiResult) => apiResult.data),
            catchError((e) => {
                console.error('Error when creating Scan', JSON.stringify(e, errorReplacer));
                this.isError = true;
                return EMPTY;
            }),
            shareReplay(1)
        );
    }

    onStarClicked(starNumber: number): void {
        this.starNumber = starNumber;
        this._patchScan(starNumber).subscribe(() => {
            this.nfc$.pipe(take(1)).subscribe((nfc) => {
                if (nfc.starsRedirected?.includes(starNumber)) {
                    if (!nfc.redirectionLink) {
                        console.error('NFC has no redirection link');
                        this.isError = true;
                        return;
                    }

                    window.location.href = this._getUpdatedRedirectionLink({
                        redirectionLink: nfc.redirectionLink,
                        platformKey: nfc.platformKey,
                    });
                } else {
                    this._router.navigate(['leave-a-review'], {
                        queryParams: {
                            restaurantId: nfc.restaurant.id,
                            rating: starNumber,
                            scanId: nfc.id,
                        },
                    });
                }
            });
        });
    }

    private _getUpdatedRedirectionLink({
        redirectionLink,
        platformKey,
    }: {
        redirectionLink: string;
        platformKey: NfcsPlatformKey;
    }): string {
        if (!redirectionLink) {
            console.error('No redirection link provided');
            return '';
        }

        const langs = this._langService.getLangsFromNavigator();
        return changePlatformUrlDomain(redirectionLink, platformKey as PlatformKey, langs) ?? '';
    }

    private _patchScan(starNumber?: number): Observable<ScanDto> {
        return this.scan$.pipe(take(1)).pipe(
            switchMap((scan) =>
                this._scansService
                    .patch(scan.id, {
                        redirectedAt: new Date().toISOString(),
                        starClicked: starNumber,
                    })
                    .pipe(
                        map((apiResult) => apiResult.data),
                        catchError((e) => {
                            console.error('Error when updating scan', JSON.stringify(e, errorReplacer));
                            this.isError = true;
                            return EMPTY;
                        })
                    )
            )
        );
    }
}
