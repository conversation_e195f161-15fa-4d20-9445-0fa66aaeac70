<div class="h-screen w-screen overflow-y-auto py-4">
    @if (!initializing()) {
        @if (!isError()) {
            @if (reviewSubmissionStep() === ReviewSubmissionStep.SUBMITTING_REVIEW) {
                <div class="flex flex-col items-center">
                    <div class="absolute top-0">
                        @if (restaurant()?.cover?.urls?.original) {
                            <img class="h-[15vh] w-screen object-cover" alt="cover" [src]="restaurant()?.cover?.urls?.original" />
                        } @else {
                            <div class="h-[15vh]"></div>
                        }
                    </div>
                    <img
                        class="malou-box-shadow absolute top-[10vh] h-24 w-24 rounded-full"
                        alt="logo"
                        [src]="restaurant()?.logo?.urls?.original || ('default_logo' | imagePathResolver)" />
                </div>
                <div class="flex max-h-full flex-col items-center justify-between gap-y-8 px-8 pt-[20vh]">
                    <div class="mt-8 flex flex-col items-center">
                        @if (!wofId()) {
                            <div class="malou-text-13--regular malou-color-text-2 text-center">
                                {{ 'scan.scan_private_review.description' | translate }}
                            </div>
                        }
                        <div class="w-72 rounded-[10px] p-6">
                            <app-rate-with-stars
                                class="h-10"
                                [readOnly]="true"
                                [defaultStarRating]="selectedRating() ?? undefined"></app-rate-with-stars>
                        </div>
                    </div>

                    <div class="malou-text-25--bold malou-color-text-1 text-center">
                        {{ 'scan.scan_private_review.title' | translate }}
                    </div>
                    <div class="mb-8 mt-8 w-full">
                        <app-text-area
                            [formControl]="textAreaControl"
                            [title]="'scan.scan_private_review.your_experience' | translate"
                            [placeholder]="'scan.scan_private_review.describe_experience' | translate"
                            [isEmojiPickerEnabled]="true"
                            [rows]="7"></app-text-area>
                    </div>
                    <div class="mb-4 w-full">
                        <app-button
                            buttonClasses="w-full !h-14 bg-malou-background-color-text-1"
                            mat-raised-button
                            [text]="'scan.scan_private_review.submit' | translate"
                            [disabled]="textAreaControl.invalid"
                            (buttonClick)="submitReview()">
                        </app-button>
                    </div>
                </div>
            } @else if (reviewSubmissionStep() === ReviewSubmissionStep.SUBMITTING_EMAIL) {
                <div class="flex flex-col items-center">
                    <div class="absolute top-0">
                        @if (restaurant()?.cover?.urls?.original) {
                            <img class="h-[35vh] w-screen object-cover" alt="cover" [src]="restaurant()?.cover?.urls?.original" />
                        } @else {
                            <div class="h-[35vh]"></div>
                        }
                    </div>
                    <img
                        class="malou-box-shadow absolute top-[30vh] h-24 w-24 rounded-full"
                        alt="logo"
                        [src]="restaurant()?.logo?.urls?.original || ('default_logo' | imagePathResolver)" />
                </div>
                <div class="flex max-h-full flex-col items-center justify-between gap-y-8 px-8 pt-[40vh]">
                    @if (!wofId()) {
                        <div class="malou-text-14--regular malou-color-text-2 mt-8 text-center">
                            {{ 'scan.scan_private_review.review_submitted.title' | translate }}
                        </div>
                    }

                    <div class="malou-text-14--bold malou-color-text-2 mt-8 text-center">
                        {{ 'scan.scan_private_review.review_submitted.leave_email' | translate }}
                    </div>
                    <div class="mb-8 mt-8 w-full">
                        <app-input-text
                            [formControl]="emailControl"
                            [title]="'common.email' | translate"
                            [placeholder]="'common.mail_placeholder' | translate"
                            [errorMessage]="emailControl.dirty && (emailControl.errors?.error || emailControl.errors?.ref_email_phone)">
                        </app-input-text>
                    </div>
                    <div class="mb-4 w-full">
                        <app-button
                            buttonClasses="w-full !h-14 bg-malou-background-color-text-1"
                            mat-raised-button
                            [text]="'common.confirm' | translate"
                            [disabled]="emailControl.invalid"
                            (buttonClick)="saveEmailForReview()">
                        </app-button>
                    </div>
                </div>
            } @else if (reviewSubmissionStep() === ReviewSubmissionStep.SUBMITTED) {
                <div class="flex flex-col items-center">
                    <div class="absolute top-0">
                        @if (restaurant()?.cover?.urls?.original) {
                            <img class="h-[55vh] w-screen object-cover" alt="cover" [src]="restaurant()?.cover?.urls?.original" />
                        } @else {
                            <div class="h-[55vh]"></div>
                        }
                    </div>
                    <img
                        class="malou-box-shadow absolute top-[50vh] h-24 w-24 rounded-full"
                        alt="logo"
                        [src]="restaurant()?.logo?.urls?.original || ('default_logo' | imagePathResolver)" />
                </div>
                <div class="flex max-h-full flex-col items-center justify-center gap-y-8 px-8 pb-12 pt-[60vh]">
                    <div class="malou-text-50--regular malou-color-text-2 mt-12 text-center">✅</div>

                    <div class="malou-text-14--bold malou-color-text-2 mt-10 text-center">
                        {{ 'scan.scan_private_review.review_email_submitted.title' | translate }}
                    </div>
                </div>
            }
        } @else {
            <div class="max-gap-y-8 flex min-h-full flex-col items-center justify-center px-8">
                <img class="w-24" [src]="Illustration.Cook | illustrationPathResolver" />
                {{ 'scan.scan_private_review.error' | translate }}
            </div>
        }
    } @else {
        <div class="flex min-h-full flex-col items-center justify-center gap-y-8 px-8">
            <app-malou-spinner></app-malou-spinner>
        </div>
    }
</div>
