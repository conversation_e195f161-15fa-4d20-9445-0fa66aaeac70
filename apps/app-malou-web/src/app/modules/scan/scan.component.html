<div class="h-screen w-screen overflow-y-auto py-4">
    @if (!isError) {
        @if (data$ | async; as data) {
            @if (!isRedirectionInProgress) {
                <ng-container [ngTemplateOutlet]="pageTemplate" [ngTemplateOutletContext]="{ restaurant: data.nfc.restaurant }">
                </ng-container>
            } @else {
                <div class="flex min-h-full flex-col items-center justify-center gap-y-8 px-8">
                    {{ 'scan.redirection_in_progress' | translate }}
                </div>
            }
        } @else {
            <div class="flex min-h-full flex-col items-center justify-center gap-y-8 px-8">
                <app-malou-spinner></app-malou-spinner>
            </div>
        }
    } @else {
        @if (boosterPackDeactivated) {
            <ng-container [ngTemplateOutlet]="boosterPackDeactivatedTemplate"></ng-container>
        } @else {
            <div class="flex min-h-full flex-col items-center justify-center gap-y-8 px-8">
                <div><img class="w-24" [src]="Illustration.Cook | illustrationPathResolver" /></div>
                <div>{{ 'scan.error' | translate }}</div>
            </div>
        }
    }
</div>

<ng-template let-restaurant="restaurant" #pageTemplate>
    <div class="flex flex-col items-center">
        <div class="absolute top-0">
            @if (restaurant.coverPopulated?.urls?.original) {
                <img class="h-[20vh] w-screen object-cover" alt="cover" [src]="restaurant.coverPopulated?.urls?.original" />
            } @else {
                <div class="h-[20vh]"></div>
            }
        </div>
        <img
            class="malou-box-shadow absolute top-[15vh] h-24 w-24 rounded-full"
            alt="logo"
            [src]="restaurant.logoPopulated?.urls?.original || ('default_logo' | imagePathResolver)" />
    </div>
    <div class="flex h-full flex-col items-center justify-center gap-y-8 px-8 pb-[10vh] pt-[25vh]">
        <div class="malou-text-13--regular malou-color-text-2 mb-4 text-center">
            {{ 'scan.thank_you' | translate }}
        </div>
        <div class="malou-text-25--bold malou-color-text-1 text-center">
            {{ 'scan.title' | translate: { restaurantName: restaurant?.totemDisplayName?.title || restaurant?.name } }}
        </div>

        <div class="bg-malou-color-background-gray--light mt-12 w-72 rounded-[10px] p-6">
            <app-rate-with-stars class="h-10" (starClicked)="onStarClicked($event)"></app-rate-with-stars>
        </div>
    </div>
</ng-template>

<ng-template #boosterPackDeactivatedTemplate>
    <div class="flex flex-col items-center">
        <div class="relative ml-5" [ngClass]="{ '!mb-[20%] mt-[30%]': isPhoneScreen(), 'my-[5%]': !isPhoneScreen() }">
            <div class="absolute bottom-0 right-[20px] h-[140px] w-[140px] rounded-full bg-malou-color-primary"></div>
            <img class="relative w-[140px]" alt="" [src]="'pleading-face' | imagePathResolver: { folder: 'wheels-of-fortune' }" />
        </div>

        <div class="malou-text-24--semibold malou-color-text-1 text-center">
            {{ 'get_my_gift.image_with_background_templates.booster_pack_deactivated.title' | translate }}
        </div>
        <div
            class="malou-text-15--semibold malou-color-text-2 text-center leading-7"
            [ngClass]="{ 'mt-[25%]': isPhoneScreen(), 'mt-[5%]': !isPhoneScreen() }">
            {{ 'get_my_gift.image_with_background_templates.booster_pack_deactivated.description' | translate }}
        </div>
    </div>
</ng-template>
