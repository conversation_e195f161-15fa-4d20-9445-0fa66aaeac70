<app-sidenav-content-route-group
    [options]="{
        routerLink: ['/groups', 'seo'],
        leftPart: {
            text: 'sidenav_content.seo' | translate,
            colorClassWhenActive: 'malou-color-text-1',
            svgIcon: SvgIcon.MAGNIFYING_GLASS,
            svgIconSize: 'medium',
            secondarySvgIcon: isStoreLocatorChildRouteOpen() ? undefined : SvgIcon.CROWN,
            secondarySvgIconColorClass: 'text-malou-color-state-warn',
        },
    }"
    [childrenOptions]="[
        {
            routerLink: ['/groups', 'seo', 'store-locator'],
            leftPart: {
                text: 'sidenav_content.store_locator' | translate,
                colorClassWhenActive: 'malou-color-primary',
                svgIcon: SvgIcon.DOT,
                svgIconSize: 'small',
                hideIconWhenActive: true,
                secondarySvgIcon: isStoreLocatorChildRouteOpen() ? SvgIcon.CROWN : undefined,
                secondarySvgIconColorClass: 'text-malou-color-state-warn',
            },
        },
    ]">
</app-sidenav-content-route-group>
