import { ChangeDetectionStrategy, Component, DestroyRef, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NavigationEnd, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { filter } from 'rxjs';

import { SidenavContentRouteGroupComponent } from ':modules/sidenav-router/sidenav-content/sidenav-content-route-group/sidenav-content-route-group.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-sidenav-content-aggregated-seo',
    imports: [SidenavContentRouteGroupComponent, TranslateModule],
    templateUrl: './sidenav-content-aggregated-seo.component.html',
    styleUrl: './sidenav-content-aggregated-seo.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SidenavContentAggregatedSeoComponent {
    private readonly _router = inject(Router);
    private readonly _destroyRef = inject(DestroyRef);

    readonly SvgIcon = SvgIcon;

    readonly isStoreLocatorChildRouteOpen: WritableSignal<boolean> = signal(false);

    constructor() {
        this._router.events
            .pipe(
                filter((event) => event instanceof NavigationEnd),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe((event: NavigationEnd) => this.isStoreLocatorChildRouteOpen.set(event.urlAfterRedirects.includes('store-locator')));
    }
}
