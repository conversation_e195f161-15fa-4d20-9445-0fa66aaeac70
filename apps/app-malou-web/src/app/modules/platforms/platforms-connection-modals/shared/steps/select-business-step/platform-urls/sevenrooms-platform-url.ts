export class SevenRoomsPlatformUrl {
    readonly URL_REGEX = /https:\/\/www\.sevenrooms\.com\/manager2?\/([a-z0-9-]+)/;

    constructor(private readonly _url: string | undefined) {}

    extractSocialId(): string | null {
        if (!this._url) {
            return null;
        }
        const match = this._url.match(this.URL_REGEX);
        const socialId = match?.[1];
        return socialId ?? null;
    }
}
