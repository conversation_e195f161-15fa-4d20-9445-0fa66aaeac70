// component that will have only on small form and a button to save
// The form allows to check or uncheck a checkbox that will change the UserCaslRole to OWNER or not
import { Component, Inject, inject, OnInit, signal } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { UserCaslRole } from '@malou-io/package-utils';

import { User } from ':modules/user/user';
import { UsersService } from ':modules/user/users.service';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-organization-settings-modal',
    templateUrl: './organization-settings-modal.component.html',
    styleUrls: ['./organization-settings-modal.component.scss'],
    imports: [
        MatButtonModule,
        MatCheckboxModule,
        MatDividerModule,
        MatFormFieldModule,
        MatIconModule,
        MatTooltipModule,
        TranslateModule,
        FormsModule,
        ReactiveFormsModule,
    ],
    standalone: true,
})
export class OrganizationSettingsModalComponent implements OnInit {
    readonly SvgIcon = SvgIcon;
    readonly isSubmitting = signal(false);
    private readonly _usersService = inject(UsersService);
    readonly organizationSettingsForm = new FormGroup<{ isOrganizationOwner: FormControl<boolean | null> }>({
        isOrganizationOwner: new FormControl<boolean | null>(false),
    });
    user: User | undefined = undefined;
    constructor(
        private readonly _dialogRef: MatDialogRef<OrganizationSettingsModalComponent>,
        @Inject(MAT_DIALOG_DATA)
        public data: {
            userId: string;
        }
    ) {}

    ngOnInit(): void {
        this._usersService.getUser(this.data.userId).subscribe((user) => {
            this.user = user;
            this.organizationSettingsForm.get('isOrganizationOwner')?.setValue(user.caslRole === UserCaslRole.OWNER);
        });
    }

    save(): void {
        this.isSubmitting.set(true);
        this._usersService
            .updateUserOrganizationSettings(this.data.userId, {
                caslRole: this.organizationSettingsForm.value?.isOrganizationOwner ? UserCaslRole.OWNER : UserCaslRole.GUEST,
            })
            .subscribe({
                next: () => {
                    this._dialogRef.close({ reload: true });
                },
                error: (err) => {
                    console.warn(err);
                    this.isSubmitting.set(false);
                },
            });
    }

    close(): void {
        this._dialogRef.close();
    }
}
