import { NgTemplateOutlet } from '@angular/common';
import {
    Component,
    computed,
    DestroyRef,
    effect,
    inject,
    Injector,
    input,
    OnChanges,
    OnInit,
    runInInjectionContext,
    signal,
    Signal,
    SimpleChanges,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { intersection, isEqual } from 'lodash';
import { DateTime } from 'luxon';
import { asapScheduler, combineLatest, Observable, Subject } from 'rxjs';
import { distinctUntilChanged, filter, map, takeUntil } from 'rxjs/operators';

import {
    getPlatformKeysWithReview,
    getPlatformKeysWithRSStats,
    HeapEventName,
    MalouComparisonPeriod,
    <PERSON>ouPeriod,
    MonthYearPeriod,
    NEW_SEMANTIC_ANALYSIS_MIN_DATE,
    PlatformFilterPage,
    PlatformKey,
} from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { HeapService } from ':core/services/heap.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import * as PlatformsReducer from ':modules/platforms/store/platforms.reducer';
import { StatisticsFiltersContext } from ':modules/statistics/filters/filters.context';
import { EReputationInsightsTabs } from ':modules/statistics/statistics.interfaces';
import * as StatisticsActions from ':modules/statistics/store/statistics.actions';
import * as StatisticsSelector from ':modules/statistics/store/statistics.selectors';
import { GroupedDateFiltersV2Component } from ':shared/components/grouped-date-filters-v2/grouped-date-filters.component';
import { GroupedDateFiltersComponent } from ':shared/components/grouped-date-filters/grouped-date-filters.component';
import { MonthYearDatePickerV2Component } from ':shared/components/month-year-date-picker-v2/month-year-date-picker-v2.component';
import { SelectChipListComponent } from ':shared/components/select-chip-list/select-chip-list.component';
import { SelectPlatformsComponent } from ':shared/components/select-platforms/select-platforms.component';
import { SelectTimeScaleFilterComponent } from ':shared/components/select-time-scale-filter/select-time-scale-filter.component';
import { AutoUnsubscribeOnDestroy } from ':shared/decorators/auto-unsubscribe-on-destroy.decorator';
import { DatePickerType } from ':shared/enums/date-pickers';
import { getSortedPlatformKeys } from ':shared/helpers/get-sorted-platform-keys';
import { KillSubscriptions } from ':shared/interfaces';
import { DatesAndPeriod, MalouDateFilters, MalouTimeScalePeriod, Nfc } from ':shared/models';

@Component({
    selector: 'app-statistics-filters',
    templateUrl: './filters.component.html',
    styleUrls: ['./filters.component.scss'],
    imports: [
        FormsModule,
        ReactiveFormsModule,
        GroupedDateFiltersComponent,
        SelectChipListComponent,
        SelectPlatformsComponent,
        NgTemplateOutlet,
        SelectTimeScaleFilterComponent,
        GroupedDateFiltersV2Component,
        MonthYearDatePickerV2Component,
    ],
})
@AutoUnsubscribeOnDestroy()
export class FiltersComponent implements OnInit, KillSubscriptions, OnChanges {
    readonly showPlatformsFilter = input<boolean>(false);
    readonly platformFilterPage = input<PlatformFilterPage>();
    readonly tabIndex = input<number>(-1);
    readonly restaurantTotems = input<Nfc[]>([]);
    readonly isRecentRestaurant = input<boolean>(false);
    readonly timeScaleMinAcceptedDate = input<Date | null>();
    readonly datePickerType = input<DatePickerType>(DatePickerType.GROUPED_DATE);

    private readonly _store = inject(Store);
    public readonly screenSizeService = inject(ScreenSizeService);
    private readonly _translateService = inject(TranslateService);
    private readonly _injector = inject(Injector);
    private readonly _statisticsFiltersContext = inject(StatisticsFiltersContext);
    private readonly _heapService = inject(HeapService);
    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _destroyRef = inject(DestroyRef);

    readonly killSubscriptions$: Subject<void> = new Subject<void>();

    readonly MalouPeriod = MalouPeriod;
    readonly DatePickerType = DatePickerType;
    readonly DEFAULT_MAX_MONTH_YEAR_RANGE = 18;

    readonly period: WritableSignal<MalouPeriod> = signal(MalouPeriod.LAST_SEVEN_DAYS);
    readonly startDate: WritableSignal<Date | null> = signal(null);
    readonly endDate: WritableSignal<Date | null> = signal(null);
    readonly comparisonPeriod: WritableSignal<MalouComparisonPeriod> = signal(MalouComparisonPeriod.PREVIOUS_PERIOD);
    readonly monthYearPeriod: WritableSignal<MonthYearPeriod> = signal(MalouDateFilters.getDefaultMonthYearPeriod());

    readonly isNewSemanticAnalysisFeatureEnabled = toSignal(
        this._experimentationService.isFeatureEnabledForRestaurant$('release-new-semantic-analysis'),
        { initialValue: false }
    );
    readonly isNewSemanticAnalysisFeatureEnabledFilter = signal(false);
    readonly defaultMinDate = computed(() => (this.isNewSemanticAnalysisFeatureEnabledFilter() ? NEW_SEMANTIC_ANALYSIS_MIN_DATE : null));
    readonly periodOptions = computed(() => {
        if (!this.isNewSemanticAnalysisFeatureEnabledFilter()) {
            return this.DEFAULT_PERIODS;
        }

        const defaultMinDate = NEW_SEMANTIC_ANALYSIS_MIN_DATE;
        const malouDateFilters = new MalouDateFilters();
        return this.DEFAULT_PERIODS.filter((period) => {
            const allPeriodDates: Record<MalouPeriod, Date | null> = malouDateFilters.getStartDateForPeriods(this.DEFAULT_PERIODS);
            const startDate = allPeriodDates[period];
            if (!startDate) {
                return false;
            }
            return startDate.getTime() >= defaultMinDate.getTime();
        });
    });

    readonly hasError: WritableSignal<boolean> = signal(false);
    connectedPlatforms: PlatformKey[];
    connectedPlatforms$: Observable<PlatformKey[]>;
    readonly platformsFilterControl: FormControl<string[]> = new FormControl<string[]>([]) as FormControl<string[]>;

    readonly showTotemsFilter: Signal<boolean> = computed(() => this.restaurantTotems && this.restaurantTotems().length > 0);
    readonly totemsFilterControl: FormControl<Nfc[]> = new FormControl<Nfc[]>([]) as FormControl<Nfc[]>;
    readonly timeScaleFilterControl: FormControl<MalouTimeScalePeriod> = new FormControl<MalouTimeScalePeriod>(
        MalouTimeScalePeriod.LAST_SIX_MONTHS
    ) as FormControl<MalouTimeScalePeriod>;
    readonly restaurantStartDate = computed(() => {
        const restaurantCreatedAt = this._restaurantsService.selectedRestaurant()?.createdAt;
        if (!restaurantCreatedAt) {
            return null;
        }
        return new Date(restaurantCreatedAt);
    });
    readonly shouldShowComparisonPeriodSelector = computed(() => {
        const restaurantCreatedAt = this.restaurantStartDate();
        if (!restaurantCreatedAt) {
            return false;
        }
        return DateTime.fromJSDate(restaurantCreatedAt).diffNow().as('years') < -1;
    });

    // TODO: Remove when the feature flag is removed
    readonly isReleaseNewCalendarEnabled = toSignal(this._experimentationService.isFeatureEnabled$('release-statistics-new-calendar'), {
        initialValue: this._experimentationService.isFeatureEnabled('release-statistics-new-calendar'),
    });

    readonly DEFAULT_PERIODS: Partial<Exclude<MalouPeriod, MalouPeriod.CUSTOM>>[] = [
        MalouPeriod.LAST_SEVEN_DAYS,
        MalouPeriod.LAST_THIRTY_DAYS,
        MalouPeriod.LAST_THREE_MONTHS,
        MalouPeriod.LAST_TWELVE_MONTHS,
    ];

    constructor() {
        effect(() => {
            this._activatedRoute.queryParams.pipe(takeUntilDestroyed(this._destroyRef)).subscribe((params) => {
                const tab = params['tab'];
                this.isNewSemanticAnalysisFeatureEnabledFilter.set(
                    this.isNewSemanticAnalysisFeatureEnabled() &&
                        this.platformFilterPage() === PlatformFilterPage.E_REPUTATION &&
                        tab === EReputationInsightsTabs.SEMANTIC_ANALYSIS.toString()
                );
                if (!this.isNewSemanticAnalysisFeatureEnabledFilter()) {
                    return;
                }
                const currentStartDate = this.startDate();
                const isStartDateBeforeLimitDate = currentStartDate && currentStartDate < NEW_SEMANTIC_ANALYSIS_MIN_DATE;
                if (isStartDateBeforeLimitDate) {
                    this.startDate.set(NEW_SEMANTIC_ANALYSIS_MIN_DATE);
                    this.period.set(MalouPeriod.DEFAULT);
                }
            });
        });
    }

    ngOnInit(): void {
        this._initDatesListener();
        this.initTimeScaleFilter();
        this._initMonthYearPeriod();

        if (this.showPlatformsFilter()) {
            this.initPlatformsFilter();
        }

        runInInjectionContext(this._injector, () => {
            effect(() => {
                if (this.showTotemsFilter()) {
                    this.initTotemsFilter();
                }
            });
        });
    }

    private _initDatesListener(): void {
        this._store
            .select(StatisticsSelector.selectDatesFilter)
            .pipe(takeUntil(this.killSubscriptions$))
            .subscribe((dates: DatesAndPeriod) => {
                this.period.set(dates.period);
                this.startDate.set(dates.startDate);
                this.endDate.set(dates.endDate);
                if (this.platformFilterPage() === PlatformFilterPage.SEO || this.platformFilterPage() === PlatformFilterPage.E_REPUTATION) {
                    this._trackDateChange(dates);
                }
            });
        this._store.select(StatisticsSelector.selectComparisonPeriodFilter).subscribe((comparisonPeriod) => {
            this.comparisonPeriod.set(comparisonPeriod);
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes?.showTotemsFilter?.currentValue) {
            this.initTotemsFilter();
        }
    }

    initPlatformsFilter(): void {
        this.connectedPlatforms$ = this._store.select(PlatformsReducer.selectCurrentPlatformKeys).pipe(
            map((platformKeys) => platformKeys.concat([PlatformKey.PRIVATE])),
            map((platformKeys) =>
                platformKeys.filter((platformKey) => {
                    if (this.platformFilterPage() === PlatformFilterPage.E_REPUTATION) {
                        const tabIndex = this.tabIndex();
                        if (tabIndex === EReputationInsightsTabs.REVIEWS && platformKey === PlatformKey.PRIVATE) {
                            return false;
                        }
                        const keys: string[] = getPlatformKeysWithReview();
                        return keys.includes(platformKey);
                    }
                    if (platformKey === PlatformKey.PRIVATE) {
                        return false;
                    }
                    if (this.platformFilterPage() === PlatformFilterPage.SOCIAL_NETWORKS) {
                        const keys: string[] = getPlatformKeysWithRSStats();
                        return keys.includes(platformKey);
                    }
                    return true;
                })
            ),
            map((e) => getSortedPlatformKeys(e))
        );

        this.connectedPlatforms$.subscribe((platforms) => {
            this.connectedPlatforms = platforms;
            return platforms;
        });

        const platformFilterPageValue = this.platformFilterPage();
        if (!platformFilterPageValue) {
            return;
        }

        combineLatest([
            this._store.select(StatisticsSelector.selectPlatformsFilter({ page: platformFilterPageValue })),
            this.connectedPlatforms$,
        ])
            .pipe(
                filter(([_, connectedPlatforms]) => !!connectedPlatforms),
                distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
                takeUntil(this.killSubscriptions$)
            )
            .subscribe(([selectedPlatforms, connectedPlatforms]) => {
                if (selectedPlatforms.length === 0) {
                    this.onPlatformsChange(connectedPlatforms);
                } else {
                    const connectedAndSelectedPlatforms = intersection(connectedPlatforms, selectedPlatforms);
                    this.hasError.set(
                        this.showPlatformsFilter() && (!connectedAndSelectedPlatforms || connectedAndSelectedPlatforms.length === 0)
                    );
                    this.platformsFilterControl.setValue(getSortedPlatformKeys([...connectedAndSelectedPlatforms]));
                }
            });
    }

    initTimeScaleFilter(): void {
        this._store.select(StatisticsSelector.selectTimeScaleFilter).subscribe((timeScale: MalouTimeScalePeriod) => {
            if (!timeScale) {
                const timeScaleInit = this.isRecentRestaurant()
                    ? MalouTimeScalePeriod.LAST_THREE_MONTHS
                    : MalouTimeScalePeriod.LAST_SIX_MONTHS;
                this.timeScaleFilterControl.setValue(timeScaleInit);
                this.onTimeScaleChange(timeScaleInit);
            } else {
                this.timeScaleFilterControl.setValue(timeScale);
            }
        });
    }

    initTotemsFilter(): void {
        this._statisticsFiltersContext.selectedTotems$.pipe(takeUntil(this.killSubscriptions$)).subscribe((totems: Nfc[]) => {
            if (totems.length === 0 && this.restaurantTotems().length > 0) {
                this.onTotemsChange(this.restaurantTotems());
            }
            this.totemsFilterControl.setValue(totems);
        });
    }

    chooseBoundaryDate(dates: DatesAndPeriod): void {
        this._store.dispatch(StatisticsActions.editDates({ dates: dates }));
    }

    chooseBoundaryDateAndPeriod({
        comparisonPeriod,
        datesAndPeriod,
    }: {
        datesAndPeriod: DatesAndPeriod;
        comparisonPeriod: MalouComparisonPeriod;
    }): void {
        this._store.dispatch(StatisticsActions.editDatesAndComparisonPeriod({ dates: datesAndPeriod, comparisonPeriod }));
        this._heapService.track(HeapEventName.TRACKING_CALENDAR_COMPARISON_PERIOD_CHANGE, {
            period: comparisonPeriod,
        });
    }

    onPlatformsChange(platforms: PlatformKey[]): void {
        const platformFilterPageValue = this.platformFilterPage();
        if (!platformFilterPageValue) {
            return;
        }
        this._store.dispatch(
            StatisticsActions.editPlatforms({
                page: platformFilterPageValue,
                platforms: platforms.length > 0 ? platforms : this.connectedPlatforms,
            })
        );
    }

    onTotemsChange(totems: Nfc[]): void {
        asapScheduler.schedule(() => {
            this._store.dispatch(StatisticsActions.editTotems({ totemIds: totems.map((totem) => totem.id) }));
        });
    }
    onTimeScaleChange(timeScale: MalouTimeScalePeriod): void {
        this._store.dispatch(StatisticsActions.editTimeScale({ data: timeScale }));
    }

    totemsDisplayWith = (totem: Nfc): string =>
        totem.isTotem() && (totem.name ?? totem.chipName)
            ? `${this._translateService.instant('statistics.totems.totem')} ${totem.name ?? totem.chipName}`
            : this._translateService.instant('enums.nfc_type.sticker');

    getTotemHash(totem: Nfc): string {
        return totem.id;
    }

    onMonthYearPeriodChanged(monthYearPeriod: MonthYearPeriod): void {
        this._store.dispatch(StatisticsActions.editMonthYearPeriod({ data: monthYearPeriod }));
    }

    private _initMonthYearPeriod(): void {
        this._store
            .select(StatisticsSelector.selectFilters)
            .pipe(
                filter((filters) => filters.isFiltersLoaded),
                takeUntil(this.killSubscriptions$)
            )
            .subscribe((filters) => {
                this.monthYearPeriod.set(filters.monthYearPeriod);
            });
    }

    private _trackDateChange(dates: DatesAndPeriod): void {
        this._heapService.track(
            this.platformFilterPage() === PlatformFilterPage.SEO
                ? HeapEventName.TRACKING_DATE_FILTER_CHANGES_SEO
                : HeapEventName.TRACKING_DATE_FILTER_CHANGES_E_REPUTATION,
            {
                startDate: dates.startDate?.toISOString(),
                endDate: dates.endDate?.toISOString(),
                period: dates.period,
                isCustom: dates.period === MalouPeriod.CUSTOM,
            }
        );
    }
}
