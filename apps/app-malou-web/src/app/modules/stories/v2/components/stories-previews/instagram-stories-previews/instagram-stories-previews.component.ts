import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { MetaStoriesPreviewsComponent } from ':modules/stories/v2/components/stories-previews/meta-stories-previews/meta-stories-previews.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';

@Component({
    selector: 'app-instagram-stories-previews',
    templateUrl: './instagram-stories-previews.component.html',
    styleUrls: ['./instagram-stories-previews.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MetaStoriesPreviewsComponent],
})
export class InstagramStoriesPreviewsComponent {
    readonly stories = input.required<StoryItem[]>();
    readonly accountName = input<string>();
    readonly profilePictureUrl = input<string>();
}
