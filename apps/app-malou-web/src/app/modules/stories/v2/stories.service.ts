import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

import { GetPublishedStoriesCountDto, GetStoriesCountsDto, PollingPostStatusDto, StoryItemDto } from '@malou-io/package-dto';
import { ApiResultV2, StoriesListFilter } from '@malou-io/package-utils';

import { environment } from ':environments/environment';
import { objectToQueryParams } from ':shared/helpers/query-params';

@Injectable({
    providedIn: 'root',
})
export class StoriesService {
    private readonly _http = inject(HttpClient);
    private readonly _API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/stories`;

    getStories$(
        filter: StoriesListFilter,
        cursor: string | null,
        restaurantId: string,
        limit: number
    ): Observable<{ storiesItems: StoryItemDto[]; nextCursor: string | null }> {
        const params = objectToQueryParams({ filter, cursor, limit });

        return this._http
            .get<ApiResultV2<{ storiesItems: StoryItemDto[]; nextCursor: null | string }>>(
                `${this._API_BASE_URL}/restaurants/${restaurantId}/stories`,
                {
                    params,
                }
            )
            .pipe(map((res) => res.data));
    }

    pollingStoriesStatus$(_bindingIds: string[]): Observable<ApiResultV2<PollingPostStatusDto[]>> {
        const mock: ApiResultV2<PollingPostStatusDto[]> = { data: [] };
        return new Observable((observer) => {
            observer.next(mock);
            observer.complete();
        });
    }

    getStoriesByIds$(_storyIds: string[]): Observable<ApiResultV2<StoryItemDto[]>> {
        const mock: ApiResultV2<StoryItemDto[]> = { data: [] };
        return new Observable((observer) => {
            observer.next(mock);
            observer.complete();
        });
    }

    getStoriesCountByFilterOptions$(_restaurantId: string): Observable<{ filterOption: StoriesListFilter; count: number | null }[]> {
        return this._http.get<ApiResultV2<GetStoriesCountsDto>>(`${this._API_BASE_URL}/restaurants/${_restaurantId}/stories-counts`).pipe(
            map((res) => [
                {
                    filterOption: StoriesListFilter.ALL,
                    count: res.data.total,
                },
                { filterOption: StoriesListFilter.DRAFT, count: res.data.draft },
                { filterOption: StoriesListFilter.ERROR, count: res.data.error },
                { filterOption: StoriesListFilter.FEEDBACK, count: res.data.feedbacks },
            ])
        );
    }

    getPublishedStoriesCount$(restaurantId: string, startDate: Date, endDate: Date): Observable<number> {
        const params = objectToQueryParams({
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
        });

        return this._http
            .get<
                ApiResultV2<GetPublishedStoriesCountDto>
            >(`${this._API_BASE_URL}/restaurants/${restaurantId}/published-stories-count`, { params })
            .pipe(map((res) => res.data.count));
    }
}
